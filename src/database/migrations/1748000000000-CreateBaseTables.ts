import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBaseTables1748000000000 implements MigrationInterface {
    name = 'CreateBaseTables1748000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create system_users table
        await queryRunner.query(`
            CREATE TABLE "system_users" (
                "id" SERIAL NOT NULL,
                "username" character varying NOT NULL,
                "email" character varying NOT NULL,
                "passwordHash" character varying NOT NULL,
                "fullName" character varying,
                "role" character varying NOT NULL DEFAULT 'editor',
                "isActive" boolean NOT NULL DEFAULT true,
                "lastLoginAt" TIMESTAMP WITH TIME ZONE,
                "createdBy" integer,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_system_users_username" UNIQUE ("username"),
                CONSTRAINT "UQ_system_users_email" UNIQUE ("email"),
                CONSTRAINT "PK_system_users" PRIMARY KEY ("id")
            )
        `);

        // Create leagues table
        await queryRunner.query(`
            CREATE TABLE "leagues" (
                "id" SERIAL NOT NULL,
                "externalId" integer,
                "name" character varying NOT NULL,
                "country" character varying NOT NULL,
                "logo" character varying,
                "flag" character varying,
                "season" integer NOT NULL,
                "active" boolean NOT NULL DEFAULT true,
                "isHot" boolean NOT NULL DEFAULT false,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_leagues_external_id" UNIQUE ("externalId"),
                CONSTRAINT "PK_leagues" PRIMARY KEY ("id")
            )
        `);

        // Create teams table
        await queryRunner.query(`
            CREATE TABLE "teams" (
                "id" SERIAL NOT NULL,
                "externalId" integer,
                "name" character varying NOT NULL,
                "code" character varying,
                "country" character varying,
                "founded" integer,
                "national" boolean NOT NULL DEFAULT false,
                "logo" character varying,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_teams_external_id" UNIQUE ("externalId"),
                CONSTRAINT "PK_teams" PRIMARY KEY ("id")
            )
        `);

        // Create fixtures table
        await queryRunner.query(`
            CREATE TABLE "fixtures" (
                "id" SERIAL NOT NULL,
                "externalId" integer,
                "isHot" boolean NOT NULL DEFAULT false,
                "leagueId" integer NOT NULL,
                "leagueName" character varying NOT NULL,
                "season" integer NOT NULL,
                "round" character varying NOT NULL,
                "homeTeamId" integer NOT NULL,
                "awayTeamId" integer NOT NULL,
                "slug" character varying NOT NULL,
                "date" TIMESTAMP WITH TIME ZONE NOT NULL,
                "venueId" integer,
                "venueName" character varying,
                "venueCity" character varying,
                "referee" character varying,
                "source" character varying NOT NULL,
                "createdBy" integer,
                "data" jsonb NOT NULL,
                "thumb" character varying(500),
                "timestamp" bigint NOT NULL,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_fixtures_external_id" UNIQUE ("externalId"),
                CONSTRAINT "UQ_fixtures_slug" UNIQUE ("slug"),
                CONSTRAINT "PK_fixtures" PRIMARY KEY ("id")
            )
        `);

        // Create players table
        await queryRunner.query(`
            CREATE TABLE "players" (
                "id" SERIAL NOT NULL,
                "externalId" integer,
                "name" character varying NOT NULL,
                "firstname" character varying,
                "lastname" character varying,
                "age" integer,
                "birth" jsonb,
                "nationality" character varying,
                "height" character varying,
                "weight" character varying,
                "injured" boolean NOT NULL DEFAULT false,
                "photo" character varying,
                "teamId" integer,
                "timestamp" bigint NOT NULL,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_players_external_id" UNIQUE ("externalId"),
                CONSTRAINT "PK_players" PRIMARY KEY ("id")
            )
        `);

        // Create standings table
        await queryRunner.query(`
            CREATE TABLE "standings" (
                "id" SERIAL NOT NULL,
                "leagueId" integer NOT NULL,
                "season" integer NOT NULL,
                "teamId" integer NOT NULL,
                "teamName" character varying NOT NULL,
                "teamLogo" character varying,
                "rank" integer NOT NULL,
                "points" integer NOT NULL,
                "goalsDiff" integer NOT NULL,
                "group" character varying,
                "form" character varying,
                "status" character varying,
                "description" character varying,
                "all" jsonb NOT NULL,
                "home" jsonb NOT NULL,
                "away" jsonb NOT NULL,
                "update" character varying NOT NULL,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "PK_standings" PRIMARY KEY ("id")
            )
        `);

        // Create broadcast_links table
        await queryRunner.query(`
            CREATE TABLE "broadcast_links" (
                "id" SERIAL NOT NULL,
                "title" character varying NOT NULL,
                "url" character varying NOT NULL,
                "description" text,
                "isActive" boolean NOT NULL DEFAULT true,
                "priority" integer NOT NULL DEFAULT 0,
                "fixtureId" integer,
                "leagueId" integer,
                "createdBy" integer NOT NULL,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "PK_broadcast_links" PRIMARY KEY ("id")
            )
        `);

        // Create indexes
        await queryRunner.query(`CREATE INDEX "IDX_fixtures_league_id" ON "fixtures" ("leagueId")`);
        await queryRunner.query(`CREATE INDEX "IDX_fixtures_date" ON "fixtures" ("date")`);
        await queryRunner.query(`CREATE INDEX "IDX_fixtures_home_team" ON "fixtures" ("homeTeamId")`);
        await queryRunner.query(`CREATE INDEX "IDX_fixtures_away_team" ON "fixtures" ("awayTeamId")`);
        await queryRunner.query(`CREATE INDEX "IDX_players_team_id" ON "players" ("teamId")`);
        await queryRunner.query(`CREATE INDEX "IDX_standings_league_season" ON "standings" ("leagueId", "season")`);
        await queryRunner.query(`CREATE INDEX "IDX_broadcast_links_fixture" ON "broadcast_links" ("fixtureId")`);
        await queryRunner.query(`CREATE INDEX "IDX_broadcast_links_league" ON "broadcast_links" ("leagueId")`);

        // Insert default admin user
        await queryRunner.query(`
            INSERT INTO "system_users" ("username", "email", "passwordHash", "fullName", "role", "isActive")
            VALUES ('admin', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', true)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop tables in reverse order
        await queryRunner.query(`DROP TABLE IF EXISTS "broadcast_links"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "standings"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "players"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "fixtures"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "teams"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "leagues"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "system_users"`);
    }
}
