import { Injectable, BadRequestException, NotFoundException, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigType } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import axios from 'axios';
import configuration from '../core/config/configuration';
import { UploadedImage } from './entities/uploaded-image.entity';
import { UploadImageByUrlDto, UploadImageResponseDto, ImageListResponseDto, GetImagesDto, ImageCategory } from './upload.dto';

@Injectable()
export class UploadService {
    private readonly logger = new Logger(UploadService.name);

    constructor(
        @InjectRepository(UploadedImage)
        private readonly uploadedImageRepository: Repository<UploadedImage>,
        @Inject(configuration.KEY)
        private config: ConfigType<typeof configuration>,
    ) { }

    /**
     * Upload image from file
     */
    async uploadImageFromFile(
        file: Express.Multer.File,
        category: ImageCategory,
        uploadedBy: number,
        description?: string
    ): Promise<UploadImageResponseDto> {
        try {
            // Validate file type
            if (!this.isValidImageType(file.mimetype)) {
                throw new BadRequestException('Invalid file type. Only PNG, JPG, JPEG, GIF, SVG are allowed.');
            }

            // Validate file size (max 50MB)
            if (file.size > 50 * 1024 * 1024) {
                throw new BadRequestException('File size too large. Maximum 50MB allowed.');
            }

            // Generate unique filename with date structure
            const imageId = this.generateImageId();
            const fileExtension = path.extname(file.originalname);
            const sanitizedName = this.sanitizeFilename(file.originalname.replace(fileExtension, ''));
            const filename = `${sanitizedName}-${Date.now()}${fileExtension}`;

            // Create date-based directory structure: YYYY/MM/DD
            const { dateFolder, fullDirectoryPath } = this.createDateBasedPath();

            // Save file
            const filePath = path.join(fullDirectoryPath, filename);
            fs.writeFileSync(filePath, file.buffer);

            // Generate public URL: /uploads/YYYY/MM/DD/filename
            const publicUrl = `${this.getBaseUrl()}/public/images/${dateFolder.replace(/\\/g, '/')}/${filename}`;

            // Save to database
            const uploadedImage = this.uploadedImageRepository.create({
                imageId,
                originalName: file.originalname,
                filename: `${dateFolder.replace(/\\/g, '/')}/${filename}`,
                size: file.size,
                mimeType: file.mimetype,
                category,
                path: filePath,
                url: publicUrl,
                description,
                uploadedBy,
            });

            const savedImage = await this.uploadedImageRepository.save(uploadedImage);

            this.logger.log(`Image uploaded successfully: ${imageId} by user ${uploadedBy}`);

            return this.toResponseDto(savedImage);
        } catch (error) {
            this.logger.error(`Failed to upload image from file: ${error.message}`);
            throw error;
        }
    }

    /**
     * Upload image from URL
     */
    async uploadImageFromUrl(
        uploadDto: UploadImageByUrlDto,
        uploadedBy: number
    ): Promise<UploadImageResponseDto> {
        try {
            // Download image from URL
            const response = await axios({
                url: uploadDto.imageUrl,
                method: 'GET',
                responseType: 'arraybuffer',
                timeout: 30000,
                maxContentLength: 50 * 1024 * 1024, // 50MB limit
            });

            // Validate content type
            const contentType = response.headers['content-type'];
            if (!this.isValidImageType(contentType)) {
                throw new BadRequestException('Invalid image type from URL. Only PNG, JPG, JPEG, GIF, SVG are allowed.');
            }

            // Generate unique filename with date structure
            const imageId = this.generateImageId();
            const urlPath = new URL(uploadDto.imageUrl).pathname;
            const fileExtension = path.extname(urlPath) || this.getExtensionFromMimeType(contentType);
            const customFilename = uploadDto.filename ? this.sanitizeFilename(uploadDto.filename) : 'image';
            const filename = `${customFilename}-${Date.now()}${fileExtension}`;

            // Create date-based directory structure: YYYY/MM/DD
            const { dateFolder, fullDirectoryPath } = this.createDateBasedPath();

            // Save file
            const filePath = path.join(fullDirectoryPath, filename);
            fs.writeFileSync(filePath, response.data);

            // Generate public URL: /uploads/YYYY/MM/DD/filename
            const publicUrl = `${this.getBaseUrl()}/public/images/${dateFolder.replace(/\\/g, '/')}/${filename}`;

            // Save to database
            const uploadedImage = this.uploadedImageRepository.create({
                imageId,
                originalName: uploadDto.filename || path.basename(urlPath),
                filename: `${dateFolder.replace(/\\/g, '/')}/${filename}`,
                size: response.data.length,
                mimeType: contentType,
                category: uploadDto.category,
                path: filePath,
                url: publicUrl,
                description: uploadDto.description,
                uploadedBy,
                sourceUrl: uploadDto.imageUrl,
            });

            const savedImage = await this.uploadedImageRepository.save(uploadedImage);

            this.logger.log(`Image uploaded from URL successfully: ${imageId} by user ${uploadedBy}`);

            return this.toResponseDto(savedImage);
        } catch (error) {
            this.logger.error(`Failed to upload image from URL: ${error.message}`);
            if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                throw new BadRequestException('Unable to download image from the provided URL.');
            }
            throw error;
        }
    }

    /**
     * Get uploaded images with pagination and filtering
     */
    async getImages(query: GetImagesDto): Promise<ImageListResponseDto> {
        const { page = 1, limit = 20, category, search } = query;
        const skip = (page - 1) * limit;

        const queryBuilder = this.uploadedImageRepository.createQueryBuilder('image');

        if (category) {
            queryBuilder.andWhere('image.category = :category', { category });
        }

        if (search) {
            queryBuilder.andWhere(
                '(image.originalName ILIKE :search OR image.description ILIKE :search)',
                { search: `%${search}%` }
            );
        }

        queryBuilder
            .orderBy('image.uploadedAt', 'DESC')
            .skip(skip)
            .take(limit);

        const [images, totalItems] = await queryBuilder.getManyAndCount();
        const totalPages = Math.ceil(totalItems / limit);

        return {
            data: images.map(image => this.toResponseDto(image)),
            meta: {
                totalItems,
                totalPages,
                currentPage: page,
                limit,
            },
        };
    }

    /**
     * Get image by ID
     */
    async getImageById(imageId: string): Promise<UploadImageResponseDto> {
        const image = await this.uploadedImageRepository.findOne({
            where: { imageId }
        });

        if (!image) {
            throw new NotFoundException('Image not found');
        }

        return this.toResponseDto(image);
    }

    /**
     * Delete image
     */
    async deleteImage(imageId: string, userId: number): Promise<void> {
        const image = await this.uploadedImageRepository.findOne({
            where: { imageId }
        });

        if (!image) {
            throw new NotFoundException('Image not found');
        }

        // Delete file from filesystem
        try {
            if (fs.existsSync(image.path)) {
                fs.unlinkSync(image.path);
            }
        } catch (error) {
            this.logger.warn(`Failed to delete file ${image.path}: ${error.message}`);
        }

        // Delete from database
        await this.uploadedImageRepository.remove(image);

        this.logger.log(`Image deleted: ${imageId} by user ${userId}`);
    }

    // Helper methods
    private generateImageId(): string {
        return `img_${crypto.randomBytes(8).toString('hex')}`;
    }

    private createDateBasedPath(): { dateFolder: string; fullDirectoryPath: string } {
        const now = new Date();
        const year = now.getFullYear().toString();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const dateFolder = path.join(year, month, day);
        const fullDirectoryPath = path.join(this.config.imageStoragePath, dateFolder);

        // Create directory if it doesn't exist
        if (!fs.existsSync(fullDirectoryPath)) {
            fs.mkdirSync(fullDirectoryPath, { recursive: true });
        }

        return { dateFolder, fullDirectoryPath };
    }

    private sanitizeFilename(filename: string): string {
        return filename.replace(/[^a-zA-Z0-9.-]/g, '_').toLowerCase();
    }

    private isValidImageType(mimeType: string): boolean {
        const allowedTypes = [
            'image/png',
            'image/jpeg',
            'image/jpg',
            'image/gif',
            'image/svg+xml'
        ];
        return allowedTypes.includes(mimeType);
    }

    private getExtensionFromMimeType(mimeType: string): string {
        const extensions: Record<string, string> = {
            'image/png': '.png',
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/gif': '.gif',
            'image/svg+xml': '.svg'
        };
        return extensions[mimeType] || '.jpg';
    }

    private getBaseUrl(): string {
        return process.env.BASE_URL || 'http://localhost:3000';
    }

    private toResponseDto(image: UploadedImage): UploadImageResponseDto {
        return {
            id: image.imageId,
            originalName: image.originalName,
            filename: image.filename,
            size: image.size,
            mimeType: image.mimeType,
            category: image.category,
            url: image.url,
            path: image.path,
            uploadedAt: image.uploadedAt,
            uploadedBy: image.uploadedBy,
            description: image.description,
        };
    }
}
