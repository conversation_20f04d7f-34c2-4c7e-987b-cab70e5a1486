import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UploadController } from './upload.controller';
import { UploadService } from './upload.service';
import { UploadedImage } from './entities/uploaded-image.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
    imports: [
        // Database
        TypeOrmModule.forFeature([UploadedImage]),

        // Multer configuration for file uploads
        MulterModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                limits: {
                    fileSize: 50 * 1024 * 1024, // 50MB limit (increased from 10MB)
                },
                fileFilter: (req, file, callback) => {
                    // Allow only image files
                    const allowedMimeTypes = [
                        'image/png',
                        'image/jpeg',
                        'image/jpg',
                        'image/gif',
                        'image/svg+xml'
                    ];

                    if (allowedMimeTypes.includes(file.mimetype)) {
                        callback(null, true);
                    } else {
                        callback(new Error('Invalid file type. Only PNG, JPG, JPEG, GIF, SVG are allowed.'), false);
                    }
                },
            }),
            inject: [ConfigService],
        }),

        // Configuration
        ConfigModule,

        // Authentication
        AuthModule,
    ],
    controllers: [UploadController],
    providers: [UploadService],
    exports: [UploadService],
})
export class UploadModule { }
