import { IoAdapter } from '@nestjs/platform-socket.io';
import { ServerOptions } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export class RedisIoAdapter extends IoAdapter {
      private adapterConstructor: ReturnType<typeof createAdapter>;

      async connectToRedis(configService: ConfigService): Promise<void> {
            const redisUrl = configService.get<string>('REDIS_URL', 'redis://localhost:6379');
            const redisPassword = configService.get<string>('REDIS_PASSWORD', '831993da');

            const pubClient = createClient({
                  url: redisUrl,
                  password: redisPassword,
            });

            const subClient = pubClient.duplicate();

            await Promise.all([pubClient.connect(), subClient.connect()]);

            this.adapterConstructor = createAdapter(pubClient, subClient);
      }

      createIOServer(port: number, options?: ServerOptions): any {
            const server = super.createIOServer(port, options);

            if (this.adapterConstructor) {
                  server.adapter(this.adapterConstructor);
            }

            return server;
      }

      static async create(app: INestApplication): Promise<RedisIoAdapter> {
            const configService = app.get(ConfigService);
            const redisIoAdapter = new RedisIoAdapter(app);

            try {
                  await redisIoAdapter.connectToRedis(configService);
                  console.log('✅ Redis adapter connected for WebSocket clustering');
            } catch (error) {
                  console.warn('⚠️  Redis adapter connection failed, falling back to default adapter:', error.message);
            }

            return redisIoAdapter;
      }
}