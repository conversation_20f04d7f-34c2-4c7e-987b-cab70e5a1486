import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { SystemUser } from '../entities/system-user.entity';

/**
 * Admin Seeder Service
 * Creates default admin user if none exists
 */
@Injectable()
export class AdminSeederService implements OnModuleInit {
    private readonly logger = new Logger(AdminSeederService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        private configService: ConfigService,
        private dataSource: DataSource,
    ) { }

    async onModuleInit() {
        // Add delay to ensure database is fully ready
        setTimeout(async () => {
            await this.createDefaultAdmin();
        }, 2000);
    }

    /**
     * <PERSON>reate default admin user if none exists
     * Uses advisory lock to prevent race conditions between multiple instances
     */
    private async createDefaultAdmin(): Promise<void> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();

        try {
            // Use advisory lock to prevent multiple instances from creating admin simultaneously
            // Lock ID: 123456 (arbitrary number for admin seeder)
            await queryRunner.query('SELECT pg_advisory_lock(123456)');

            // Check if admin user with username 'admin' already exists
            const existingAdminByUsername = await queryRunner.manager.findOne(SystemUser, {
                where: { username: 'admin' }
            });

            if (existingAdminByUsername) {
                this.logger.log('Default admin user already exists, skipping seeder');
                this.logger.log('📧 Email: <EMAIL>');
                this.logger.log('👤 Username: admin');
                this.logger.log('🔑 Use existing password or reset if needed');
                return;
            }

            // Also check if any admin role user exists
            const existingAdminByRole = await queryRunner.manager.findOne(SystemUser, {
                where: { role: 'admin' }
            });

            if (existingAdminByRole) {
                this.logger.log('Admin role user already exists, skipping default admin creation');
                this.logger.log(`👤 Existing admin: ${existingAdminByRole.username}`);
                return;
            }

            // Create default admin
            const defaultPassword = this.configService.get<string>('DEFAULT_ADMIN_PASSWORD', 'admin123456');
            const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
            const hashedPassword = await bcrypt.hash(defaultPassword, saltRounds);

            // Create admin user using raw SQL with ON CONFLICT
            const result = await queryRunner.query(`
                INSERT INTO system_users (username, email, "passwordHash", "fullName", role, "isActive")
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (username) DO NOTHING
                RETURNING id
            `, ['admin', '<EMAIL>', hashedPassword, 'System Administrator', 'admin', true]);

            // Check if user was actually created
            if (result.length === 0) {
                this.logger.log('Admin user already exists (detected during insert), skipping seeder');
                this.logger.log('📧 Email: <EMAIL>');
                this.logger.log('👤 Username: admin');
                this.logger.log('🔑 Use existing password or reset if needed');
                return;
            }

            this.logger.log('🎉 Default admin user created successfully!');
            this.logger.log('📧 Email: <EMAIL>');
            this.logger.log('👤 Username: admin');
            this.logger.log('🔑 Password: admin123456');
            this.logger.warn('⚠️  Please change the default password after first login!');

        } catch (error) {
            // Check if error is due to duplicate key (user already exists)
            if (error.code === '23505' && error.constraint?.includes('username')) {
                this.logger.log('Admin user already exists (detected during creation), skipping seeder');
                this.logger.log('📧 Email: <EMAIL>');
                this.logger.log('👤 Username: admin');
                this.logger.log('🔑 Use existing password or reset if needed');
                return;
            }

            // Log other errors
            this.logger.error('Failed to create default admin user:', error.message);
            this.logger.error('Error details:', {
                message: error.message,
                code: error.code,
                detail: error.detail,
                constraint: error.constraint,
            });
        } finally {
            // Always release the advisory lock and close connection
            try {
                await queryRunner.query('SELECT pg_advisory_unlock(123456)');
                await queryRunner.release();
            } catch (releaseError) {
                this.logger.error('Failed to release advisory lock:', releaseError.message);
            }
        }
    }

    /**
     * Reset admin password (for development/testing)
     */
    async resetAdminPassword(newPassword: string = 'admin123456'): Promise<void> {
        try {
            const admin = await this.systemUserRepository.findOne({
                where: { username: 'admin' }
            });

            if (!admin) {
                throw new Error('Admin user not found');
            }

            const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
            const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

            admin.passwordHash = hashedPassword;
            await this.systemUserRepository.save(admin);

            this.logger.log('Admin password reset successfully');
        } catch (error) {
            this.logger.error('Failed to reset admin password:', error);
            throw error;
        }
    }
}
