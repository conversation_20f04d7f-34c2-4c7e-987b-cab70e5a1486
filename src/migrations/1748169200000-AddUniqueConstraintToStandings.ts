import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueConstraintToStandings1748169200000 implements MigrationInterface {
      name = 'AddUniqueConstraintToStandings1748169200000';

      public async up(queryRunner: QueryRunner): Promise<void> {
            // Add unique constraint on (leagueId, season, teamId)
            await queryRunner.query(`
            ALTER TABLE "standings" 
            ADD CONSTRAINT "UQ_STANDINGS_LEAGUE_SEASON_TEAM" 
            UNIQUE ("leagueId", "season", "teamId")
        `);
      }

      public async down(queryRunner: QueryRunner): Promise<void> {
            // Remove unique constraint
            await queryRunner.query(`
            ALTER TABLE "standings" 
            DROP CONSTRAINT "UQ_STANDINGS_LEAGUE_SEASON_TEAM"
        `);
      }
}
