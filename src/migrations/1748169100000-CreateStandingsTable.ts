import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateStandingsTable1748169100000 implements MigrationInterface {
      name = 'CreateStandingsTable1748169100000';

      public async up(queryRunner: QueryRunner): Promise<void> {
            await queryRunner.createTable(
                  new Table({
                        name: 'standings',
                        columns: [
                              {
                                    name: 'id',
                                    type: 'int',
                                    isPrimary: true,
                                    isGenerated: true,
                                    generationStrategy: 'increment',
                              },
                              {
                                    name: 'leagueId',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'leagueName',
                                    type: 'varchar',
                                    isNullable: false,
                              },
                              {
                                    name: 'season',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'teamId',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'teamName',
                                    type: 'varchar',
                                    isNullable: false,
                              },
                              {
                                    name: 'teamLogo',
                                    type: 'varchar',
                                    isNullable: true,
                              },
                              {
                                    name: 'position',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'points',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'played',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'win',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'draw',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'lose',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'goalsFor',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'goalsAgainst',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'goalsDiff',
                                    type: 'int',
                                    isNullable: false,
                              },
                              {
                                    name: 'form',
                                    type: 'varchar',
                                    isNullable: true,
                              },
                              {
                                    name: 'status',
                                    type: 'varchar',
                                    isNullable: true,
                              },
                              {
                                    name: 'description',
                                    type: 'varchar',
                                    isNullable: true,
                              },
                              {
                                    name: 'groupName',
                                    type: 'varchar',
                                    isNullable: true,
                              },
                              {
                                    name: 'data',
                                    type: 'jsonb',
                                    isNullable: false,
                              },
                              {
                                    name: 'source',
                                    type: 'varchar',
                                    isNullable: false,
                              },
                              {
                                    name: 'createdBy',
                                    type: 'int',
                                    isNullable: true,
                              },
                              {
                                    name: 'createdAt',
                                    type: 'timestamp',
                                    default: 'CURRENT_TIMESTAMP',
                              },
                              {
                                    name: 'updatedAt',
                                    type: 'timestamp',
                                    default: 'CURRENT_TIMESTAMP',
                                    onUpdate: 'CURRENT_TIMESTAMP',
                              },
                        ],
                  }),
                  true
            );

            // Create indexes for optimization
            await queryRunner.createIndex('standings', new TableIndex({
                  name: 'IDX_STANDINGS_LEAGUE_ID',
                  columnNames: ['leagueId']
            }));
            await queryRunner.createIndex('standings', new TableIndex({
                  name: 'IDX_STANDINGS_SEASON',
                  columnNames: ['season']
            }));
            await queryRunner.createIndex('standings', new TableIndex({
                  name: 'IDX_STANDINGS_TEAM_ID',
                  columnNames: ['teamId']
            }));
            await queryRunner.createIndex('standings', new TableIndex({
                  name: 'IDX_STANDINGS_LEAGUE_SEASON',
                  columnNames: ['leagueId', 'season']
            }));
            await queryRunner.createIndex('standings', new TableIndex({
                  name: 'IDX_STANDINGS_TEAM_SEASON',
                  columnNames: ['teamId', 'season']
            }));
      }

      public async down(queryRunner: QueryRunner): Promise<void> {
            await queryRunner.dropTable('standings');
      }
}
