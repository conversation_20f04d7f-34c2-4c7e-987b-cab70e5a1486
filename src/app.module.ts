import { Module } from '@nestjs/common';
import { CoreApiModule } from './core/core-api.module';
import { SharedModule } from './shared';
import { FootballApiModule } from './sports/football/football-api.module';
import { BroadcastLinkModule } from './broadcast-links/broadcast-link.module';
import { SwaggerModule } from './docs/swagger.module';
import { AuthModule } from './auth/auth.module';
import { UploadModule } from './upload/upload.module';
import { NewsModule } from './news/news.module';

@Module({
  imports: [
    CoreApiModule,
    SharedModule,
    AuthModule,
    FootballApiModule,
    BroadcastLinkModule,
    UploadModule,
    NewsModule,
    SwaggerModule.forRoot(),
  ],
})
export class AppModule { }