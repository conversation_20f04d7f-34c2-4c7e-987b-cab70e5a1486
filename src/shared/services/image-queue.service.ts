import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import Redis from 'ioredis';
import configuration from '../../core/config/configuration';
import {
    QueueItem,
    QueuePriority,
    JobStatus,
    JobStatusResult,
    QueueStats,
    PerformanceMetrics,
    ImageType,
    QUEUE_KEYS,
    PRIORITY_ORDER,
    DEFAULT_PRIORITIES
} from '../types/image-queue.types';

@Injectable()
export class ImageQueueService {
    private readonly logger = new Logger(ImageQueueService.name);
    private redis: Redis;
    private gateway: any; // Will be injected later to avoid circular dependency

    constructor(
        @Inject(configuration.KEY)
        private config: ConfigType<typeof configuration>,
    ) {
        this.redis = new Redis({
            host: this.config.redisHost,
            port: this.config.redisPort,
            password: this.config.redisPassword,
            maxRetriesPerRequest: 3,
        });

        this.redis.on('connect', () => {
            this.logger.log('Connected to Redis for image queue');
        });

        this.redis.on('error', (error) => {
            this.logger.error(`Redis connection error: ${error.message}`);
        });
    }

    /**
     * Set gateway instance for notifications (called from gateway)
     */
    setGateway(gateway: any) {
        this.gateway = gateway;
    }

    /**
     * Add image download job to queue
     */
    async addToQueue(
        url: string,
        type: ImageType,
        fileName: string,
        priority?: QueuePriority
    ): Promise<string> {
        // Determine priority based on type if not specified
        const jobPriority = priority || this.getPriorityForType(type);

        // Generate unique job ID
        const jobId = this.generateJobId();

        // Create queue item
        const queueItem: QueueItem = {
            id: jobId,
            url,
            type,
            fileName,
            priority: jobPriority,
            retries: 0,
            maxRetries: this.config.imageQueue.maxRetries,
            createdAt: Date.now()
        };

        // Check for duplicates
        const duplicateKey = this.getDuplicateKey(url, type, fileName);
        const existingJobId = await this.redis.get(duplicateKey);

        if (existingJobId) {
            //this.logger.debug(`Duplicate job found for ${url}, returning existing job: ${existingJobId}`);
            return existingJobId;
        }

        // Add to appropriate priority queue
        const queueKey = this.getQueueKeyForPriority(jobPriority);
        await this.redis.lpush(queueKey, JSON.stringify(queueItem));

        // Set duplicate check with TTL (24 hours)
        await this.redis.setex(duplicateKey, 24 * 60 * 60, jobId);

        this.logger.debug(`Added job ${jobId} to ${jobPriority} priority queue: ${type}/${fileName}`);

        return jobId;
    }

    /**
     * Get next job from queue (highest priority first)
     */
    async getNextJob(): Promise<QueueItem | null> {
        for (const priority of PRIORITY_ORDER) {
            const queueKey = this.getQueueKeyForPriority(priority);
            const item = await this.redis.rpop(queueKey);

            if (item) {
                const queueItem: QueueItem = JSON.parse(item);

                // Mark as processing
                queueItem.startedAt = Date.now();
                await this.redis.hset(QUEUE_KEYS.PROCESSING, queueItem.id, JSON.stringify(queueItem));

                // Notify via WebSocket
                if (this.gateway) {
                    this.gateway.notifyJobStarted(queueItem.id, queueItem.type, queueItem.fileName);
                }

                this.logger.debug(`Retrieved job ${queueItem.id} from ${priority} priority queue`);
                return queueItem;
            }
        }

        return null;
    }

    /**
     * Mark job as completed
     */
    async markJobCompleted(jobId: string, filePath: string): Promise<void> {
        const processingData = await this.redis.hget(QUEUE_KEYS.PROCESSING, jobId);

        if (!processingData) {
            this.logger.warn(`Job ${jobId} not found in processing queue`);
            return;
        }

        const queueItem: QueueItem = JSON.parse(processingData);
        queueItem.completedAt = Date.now();
        queueItem.filePath = filePath;

        // Move from processing to completed
        await this.redis.hset(QUEUE_KEYS.COMPLETED, jobId, JSON.stringify(queueItem));
        await this.redis.hdel(QUEUE_KEYS.PROCESSING, jobId);

        // Clean up duplicate check
        const duplicateKey = this.getDuplicateKey(queueItem.url, queueItem.type, queueItem.fileName);
        await this.redis.del(duplicateKey);

        // Notify via WebSocket
        if (this.gateway) {
            this.gateway.notifyJobCompleted(jobId, filePath, queueItem.type);
            this.gateway.broadcastQueueStats();
        }

        this.logger.debug(`Job ${jobId} marked as completed: ${filePath}`);
    }

    /**
     * Mark job as failed
     */
    async markJobFailed(jobId: string, error: string): Promise<void> {
        const processingData = await this.redis.hget(QUEUE_KEYS.PROCESSING, jobId);

        if (!processingData) {
            this.logger.warn(`Job ${jobId} not found in processing queue`);
            return;
        }

        const queueItem: QueueItem = JSON.parse(processingData);
        queueItem.error = error;
        queueItem.retries++;

        // Check if should retry
        if (queueItem.retries < queueItem.maxRetries) {
            // Re-add to queue for retry
            const queueKey = this.getQueueKeyForPriority(queueItem.priority);
            await this.redis.lpush(queueKey, JSON.stringify(queueItem));
            await this.redis.hdel(QUEUE_KEYS.PROCESSING, jobId);

            this.logger.warn(`Job ${jobId} failed, retrying (${queueItem.retries}/${queueItem.maxRetries}): ${error}`);
        } else {
            // Move to failed queue
            await this.redis.hset(QUEUE_KEYS.FAILED, jobId, JSON.stringify(queueItem));
            await this.redis.hdel(QUEUE_KEYS.PROCESSING, jobId);

            // Clean up duplicate check
            const duplicateKey = this.getDuplicateKey(queueItem.url, queueItem.type, queueItem.fileName);
            await this.redis.del(duplicateKey);

            // Notify via WebSocket
            if (this.gateway) {
                this.gateway.notifyJobFailed(jobId, error, queueItem.type);
                this.gateway.broadcastQueueStats();
            }

            this.logger.error(`Job ${jobId} failed permanently after ${queueItem.retries} retries: ${error}`);
        }
    }

    /**
     * Get job status
     */
    async getJobStatus(jobId: string): Promise<JobStatusResult> {
        // Check processing
        const processing = await this.redis.hget(QUEUE_KEYS.PROCESSING, jobId);
        if (processing) {
            return { status: 'processing', data: JSON.parse(processing) };
        }

        // Check completed
        const completed = await this.redis.hget(QUEUE_KEYS.COMPLETED, jobId);
        if (completed) {
            return { status: 'completed', data: JSON.parse(completed) };
        }

        // Check failed
        const failed = await this.redis.hget(QUEUE_KEYS.FAILED, jobId);
        if (failed) {
            return { status: 'failed', data: JSON.parse(failed) };
        }

        // Check if still in queue
        const inQueue = await this.findJobInQueues(jobId);
        if (inQueue) {
            return { status: 'queued', data: inQueue };
        }

        return { status: 'not_found', message: `Job ${jobId} not found` };
    }

    /**
     * Get queue statistics
     */
    async getQueueStats(): Promise<QueueStats> {
        const [highCount, normalCount, lowCount, processingCount, completedCount, failedCount] = await Promise.all([
            this.redis.llen(QUEUE_KEYS.HIGH_PRIORITY),
            this.redis.llen(QUEUE_KEYS.NORMAL_PRIORITY),
            this.redis.llen(QUEUE_KEYS.LOW_PRIORITY),
            this.redis.hlen(QUEUE_KEYS.PROCESSING),
            this.redis.hlen(QUEUE_KEYS.COMPLETED),
            this.redis.hlen(QUEUE_KEYS.FAILED)
        ]);

        return {
            queued: {
                high: highCount,
                normal: normalCount,
                low: lowCount,
                total: highCount + normalCount + lowCount
            },
            processing: processingCount,
            completed: completedCount,
            failed: failedCount,
            totalProcessed: completedCount + failedCount
        };
    }

    /**
     * Clean up old completed and failed jobs
     */
    async cleanupOldJobs(): Promise<{ cleaned: number }> {
        const cutoff = Date.now() - (this.config.imageQueue.cleanupIntervalHours * 60 * 60 * 1000);
        let cleaned = 0;

        // Clean completed jobs
        const completed = await this.redis.hgetall(QUEUE_KEYS.COMPLETED);
        for (const [jobId, data] of Object.entries(completed)) {
            const job: QueueItem = JSON.parse(data);
            if (job.completedAt && job.completedAt < cutoff) {
                await this.redis.hdel(QUEUE_KEYS.COMPLETED, jobId);
                cleaned++;
            }
        }

        // Clean failed jobs
        const failed = await this.redis.hgetall(QUEUE_KEYS.FAILED);
        for (const [jobId, data] of Object.entries(failed)) {
            const job: QueueItem = JSON.parse(data);
            if (job.createdAt < cutoff) {
                await this.redis.hdel(QUEUE_KEYS.FAILED, jobId);
                cleaned++;
            }
        }

        this.logger.log(`Cleaned up ${cleaned} old jobs`);
        return { cleaned };
    }

    // Helper methods
    private generateJobId(): string {
        return `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private getPriorityForType(type: ImageType): QueuePriority {
        return this.config.imageQueue.priorities[type] as QueuePriority || DEFAULT_PRIORITIES[type];
    }

    private getQueueKeyForPriority(priority: QueuePriority): string {
        switch (priority) {
            case 'high': return QUEUE_KEYS.HIGH_PRIORITY;
            case 'normal': return QUEUE_KEYS.NORMAL_PRIORITY;
            case 'low': return QUEUE_KEYS.LOW_PRIORITY;
            default: return QUEUE_KEYS.NORMAL_PRIORITY;
        }
    }

    private getDuplicateKey(url: string, type: string, fileName: string): string {
        const hash = Buffer.from(`${url}:${type}:${fileName}`).toString('base64');
        return `${QUEUE_KEYS.DUPLICATE_CHECK}:${hash}`;
    }

    private async findJobInQueues(jobId: string): Promise<QueueItem | null> {
        for (const priority of PRIORITY_ORDER) {
            const queueKey = this.getQueueKeyForPriority(priority);
            const items = await this.redis.lrange(queueKey, 0, -1);

            for (const item of items) {
                const queueItem: QueueItem = JSON.parse(item);
                if (queueItem.id === jobId) {
                    return queueItem;
                }
            }
        }
        return null;
    }
}
