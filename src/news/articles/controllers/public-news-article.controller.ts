import { Controller, Get, Post, Param, Query, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiParam,
    ApiQuery,
} from '@nestjs/swagger';
import { NewsArticleService } from '../services/news-article.service';
import { Public } from '../../../auth/core/decorators/auth.decorators';
import { ArticleResponseDto, PaginatedArticlesResponseDto, PublicArticlesDto } from '../dto';

@ApiTags('Public - News Articles')
@Controller('news')
export class PublicNewsArticleController {
    constructor(private readonly articleService: NewsArticleService) { }

    @ApiOperation({
        summary: 'Get published articles',
        description: 'Get paginated list of published news articles with search and filtering.',
    })
    @ApiResponse({
        status: 200,
        description: 'Published articles retrieved successfully',
        type: PaginatedArticlesResponseDto,
        examples: {
            success: {
                summary: 'Successful response',
                value: {
                    data: [
                        {
                            id: 1,
                            title: '<PERSON><PERSON> Signs New Contract with Barcelona',
                            slug: 'messi-signs-new-contract-barcelona',
                            excerpt: '<PERSON> has agreed to a new 3-year contract with Barcelona...',
                            content: '<p>Lionel Messi has officially signed a new contract...</p>',
                            featuredImage: 'https://example.com/images/messi-contract.jpg',
                            tags: ['messi', 'barcelona', 'contract', 'transfer'],
                            status: 'published',
                            publishedAt: '2025-05-30T10:00:00.000Z',
                            viewCount: 1250,
                            shareCount: 45,
                            likeCount: 89,
                            isFeatured: true,
                            priority: 5,
                            category: {
                                id: 1,
                                slug: 'transfer-news',
                                name: 'Transfer News',
                                color: '#FF6B35'
                            },
                            authorId: 1,
                            createdAt: '2025-05-30T08:00:00.000Z',
                            updatedAt: '2025-05-30T08:00:00.000Z'
                        }
                    ],
                    meta: {
                        totalItems: 25,
                        totalPages: 3,
                        currentPage: 1,
                        limit: 10
                    },
                    status: 200
                }
            }
        }
    })
    @Public()
    @Get()
    async getPublishedArticles(@Query() query: PublicArticlesDto): Promise<PaginatedArticlesResponseDto> {
        return this.articleService.getPublishedArticles(query);
    }

    @ApiOperation({
        summary: 'Get featured articles',
        description: 'Get list of featured articles for homepage or special sections.',
    })
    @ApiQuery({
        name: 'limit',
        description: 'Number of featured articles to return',
        example: 5,
        required: false,
    })
    @ApiResponse({
        status: 200,
        description: 'Featured articles retrieved successfully',
        type: [ArticleResponseDto],
        examples: {
            success: {
                summary: 'Featured articles',
                value: [
                    {
                        id: 1,
                        title: 'Messi Signs New Contract with Barcelona',
                        slug: 'messi-signs-new-contract-barcelona',
                        excerpt: 'Lionel Messi has agreed to a new 3-year contract...',
                        featuredImage: 'https://example.com/images/messi-contract.jpg',
                        tags: ['messi', 'barcelona', 'contract'],
                        status: 'published',
                        publishedAt: '2025-05-30T10:00:00.000Z',
                        viewCount: 1250,
                        isFeatured: true,
                        priority: 5,
                        category: {
                            id: 1,
                            slug: 'transfer-news',
                            name: 'Transfer News',
                            color: '#FF6B35'
                        }
                    }
                ]
            }
        }
    })
    @Public()
    @Get('featured')
    async getFeaturedArticles(@Query('limit') limitStr?: string): Promise<ArticleResponseDto[]> {
        const limit = limitStr ? parseInt(limitStr, 10) : 5;
        return this.articleService.getFeaturedArticles(limit);
    }

    @ApiOperation({
        summary: 'Get articles by category',
        description: 'Get published articles filtered by category slug.',
    })
    @ApiParam({
        name: 'categorySlug',
        description: 'Category slug',
        example: 'transfer-news',
    })
    @ApiResponse({
        status: 200,
        description: 'Category articles retrieved successfully',
        type: PaginatedArticlesResponseDto,
    })
    @ApiResponse({
        status: 404,
        description: 'Category not found',
        example: {
            message: "Category with slug 'invalid-category' not found",
            error: 'Not Found',
            statusCode: 404,
        },
    })
    @Public()
    @Get('category/:categorySlug')
    async getArticlesByCategory(
        @Param('categorySlug') categorySlug: string,
        @Query() query: PublicArticlesDto,
    ): Promise<PaginatedArticlesResponseDto> {
        return this.articleService.getArticlesByCategory(categorySlug, query);
    }

    @ApiOperation({
        summary: 'Get article by slug',
        description: 'Get a specific published article by its slug.',
    })
    @ApiParam({
        name: 'slug',
        description: 'Article slug',
        example: 'messi-signs-new-contract-barcelona',
    })
    @ApiResponse({
        status: 200,
        description: 'Article retrieved successfully',
        type: ArticleResponseDto,
        examples: {
            success: {
                summary: 'Article details',
                value: {
                    id: 1,
                    title: 'Messi Signs New Contract with Barcelona',
                    slug: 'messi-signs-new-contract-barcelona',
                    excerpt: 'Lionel Messi has agreed to a new 3-year contract with Barcelona...',
                    content: '<p>Lionel Messi has officially signed a new contract with Barcelona...</p>',
                    featuredImage: 'https://example.com/images/messi-contract.jpg',
                    tags: ['messi', 'barcelona', 'contract', 'transfer'],
                    status: 'published',
                    publishedAt: '2025-05-30T10:00:00.000Z',
                    metaTitle: 'Messi Signs New Barcelona Contract - Latest Football News',
                    metaDescription: 'Breaking news: Lionel Messi has signed a new 3-year contract...',
                    relatedTeamId: 529,
                    relatedPlayerId: 154,
                    viewCount: 1250,
                    shareCount: 45,
                    likeCount: 89,
                    isFeatured: true,
                    priority: 5,
                    category: {
                        id: 1,
                        slug: 'transfer-news',
                        name: 'Transfer News',
                        description: 'Latest transfer rumors, confirmations and market updates',
                        icon: 'transfer',
                        color: '#FF6B35'
                    },
                    authorId: 1,
                    createdAt: '2025-05-30T08:00:00.000Z',
                    updatedAt: '2025-05-30T08:00:00.000Z'
                }
            }
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Article not found',
        example: {
            message: "Article with slug 'invalid-slug' not found",
            error: 'Not Found',
            statusCode: 404,
        },
    })
    @Public()
    @Get(':slug')
    async getArticleBySlug(@Param('slug') slug: string): Promise<ArticleResponseDto> {
        return this.articleService.getArticleBySlug(slug);
    }

    @ApiOperation({
        summary: 'Get related articles',
        description: 'Get articles related to a specific article.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiQuery({
        name: 'limit',
        description: 'Number of related articles to return',
        example: 5,
        required: false,
    })
    @ApiResponse({
        status: 200,
        description: 'Related articles retrieved successfully',
        type: [ArticleResponseDto],
    })
    @Public()
    @Get(':id/related')
    async getRelatedArticles(
        @Param('id', ParseIntPipe) id: number,
        @Query('limit') limitStr?: string,
    ): Promise<ArticleResponseDto[]> {
        const limit = limitStr ? parseInt(limitStr, 10) : 5;
        return this.articleService.getRelatedArticles(id, limit);
    }

    @ApiOperation({
        summary: 'Increment article view count',
        description: 'Track article views for analytics. Call this when user views an article.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'View count incremented successfully',
        example: {
            message: 'View count incremented',
        },
    })
    @Public()
    @Post(':id/view')
    @HttpCode(HttpStatus.OK)
    async incrementViewCount(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
        await this.articleService.incrementViewCount(id);
        return { message: 'View count incremented' };
    }

    @ApiOperation({
        summary: 'Increment article share count',
        description: 'Track article shares for analytics. Call this when user shares an article.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Share count incremented successfully',
        example: {
            message: 'Share count incremented',
        },
    })
    @Public()
    @Post(':id/share')
    @HttpCode(HttpStatus.OK)
    async incrementShareCount(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
        await this.articleService.incrementShareCount(id);
        return { message: 'Share count incremented' };
    }
}
