import {
    Controller,
    Get,
    Post,
    Patch,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    ParseIntPipe,
    HttpCode,
    HttpStatus,
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
    ApiParam,
    ApiBody,
} from '@nestjs/swagger';
import { NewsArticleService } from '../services/news-article.service';
import { SystemJwtAuthGuard } from '../../../auth/system/guards/system-jwt-auth.guard';
import { GetCurrentUser } from '../../../auth/core/decorators/auth.decorators';
import { SystemUser } from '../../../auth/system/entities/system-user.entity';
import {
    CreateArticleDto,
    UpdateArticleDto,
    GetArticlesDto,
    ArticleResponseDto,
    PaginatedArticlesResponseDto,
} from '../dto';

@ApiTags('Admin - News Articles')
@Controller('admin/news/articles')
@UseGuards(SystemJwtAuthGuard)
@ApiBearerAuth()
export class NewsArticleController {
    constructor(private readonly articleService: NewsArticleService) { }

    @ApiOperation({
        summary: 'Create news article',
        description: 'Create a new news article. Requires admin/moderator/editor role.',
    })
    @ApiResponse({
        status: 201,
        description: 'Article created successfully',
        type: ArticleResponseDto,
    })
    @ApiResponse({
        status: 400,
        description: 'Bad request - validation error or slug conflict',
        example: {
            message: "Article with slug 'messi-signs-new-contract' already exists",
            error: 'Bad Request',
            statusCode: 400,
        },
    })
    @Post()
    async createArticle(
        @Body() createArticleDto: CreateArticleDto,
        @GetCurrentUser() user: SystemUser,
    ): Promise<ArticleResponseDto> {
        return this.articleService.createArticle(createArticleDto, user.id);
    }

    @ApiOperation({
        summary: 'Get all articles',
        description: 'Get paginated list of news articles with advanced filtering options.',
    })
    @ApiResponse({
        status: 200,
        description: 'Articles retrieved successfully',
        type: PaginatedArticlesResponseDto,
    })
    @Get()
    async getArticles(
        @Query() query: GetArticlesDto,
    ): Promise<PaginatedArticlesResponseDto> {
        return this.articleService.getArticles(query);
    }

    @ApiOperation({
        summary: 'Get article by ID',
        description: 'Get a specific news article by its ID.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Article retrieved successfully',
        type: ArticleResponseDto,
    })
    @ApiResponse({
        status: 404,
        description: 'Article not found',
        example: {
            message: 'Article with ID 1 not found',
            error: 'Not Found',
            statusCode: 404,
        },
    })
    @Get(':id')
    async getArticleById(
        @Param('id', ParseIntPipe) id: number,
    ): Promise<ArticleResponseDto> {
        return this.articleService.getArticleById(id);
    }

    @ApiOperation({
        summary: 'Update article',
        description: 'Update an existing news article. Slug cannot be changed.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Article updated successfully',
        type: ArticleResponseDto,
    })
    @ApiResponse({
        status: 404,
        description: 'Article not found',
    })
    @Patch(':id')
    async updateArticle(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateArticleDto: UpdateArticleDto,
        @GetCurrentUser() user: SystemUser,
    ): Promise<ArticleResponseDto> {
        console.log(`🔍 CONTROLLER: Update article ID ${id}, DTO:`, JSON.stringify(updateArticleDto));
        console.log(`🔍 CONTROLLER: User ID: ${user.id}`);
        return this.articleService.updateArticle(id, updateArticleDto, user.id);
    }

    @ApiOperation({
        summary: 'Delete article',
        description: 'Delete a news article permanently.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 204,
        description: 'Article deleted successfully',
    })
    @ApiResponse({
        status: 404,
        description: 'Article not found',
    })
    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async deleteArticle(@Param('id', ParseIntPipe) id: number): Promise<void> {
        return this.articleService.deleteArticle(id);
    }

    @ApiOperation({
        summary: 'Publish article',
        description: 'Change article status to published and set published date.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Article published successfully',
        type: ArticleResponseDto,
    })
    @Post(':id/publish')
    async publishArticle(@Param('id', ParseIntPipe) id: number): Promise<ArticleResponseDto> {
        return this.articleService.publishArticle(id);
    }

    @ApiOperation({
        summary: 'Unpublish article',
        description: 'Change article status back to draft.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Article unpublished successfully',
        type: ArticleResponseDto,
    })
    @Post(':id/unpublish')
    async unpublishArticle(@Param('id', ParseIntPipe) id: number): Promise<ArticleResponseDto> {
        return this.articleService.unpublishArticle(id);
    }

    @ApiOperation({
        summary: 'Archive article',
        description: 'Change article status to archived.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Article archived successfully',
        type: ArticleResponseDto,
    })
    @Post(':id/archive')
    async archiveArticle(@Param('id', ParseIntPipe) id: number): Promise<ArticleResponseDto> {
        return this.articleService.archiveArticle(id);
    }

    @ApiOperation({
        summary: 'Get featured articles',
        description: 'Get list of featured articles for admin management.',
    })
    @ApiResponse({
        status: 200,
        description: 'Featured articles retrieved successfully',
        type: [ArticleResponseDto],
    })
    @Get('featured/list')
    async getFeaturedArticles(): Promise<ArticleResponseDto[]> {
        return this.articleService.getFeaturedArticles(10);
    }

    @ApiOperation({
        summary: 'Get related articles',
        description: 'Get articles related to a specific article.',
    })
    @ApiParam({
        name: 'id',
        description: 'Article ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Related articles retrieved successfully',
        type: [ArticleResponseDto],
    })
    @Get(':id/related')
    async getRelatedArticles(@Param('id', ParseIntPipe) id: number): Promise<ArticleResponseDto[]> {
        return this.articleService.getRelatedArticles(id, 5);
    }
}
