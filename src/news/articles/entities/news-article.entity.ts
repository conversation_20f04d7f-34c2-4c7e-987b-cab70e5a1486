import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    Index,
    ManyToOne,
    JoinColumn
} from 'typeorm';
import { NewsCategory } from '../../categories/entities/news-category.entity';

export enum ArticleStatus {
    DRAFT = 'draft',
    PUBLISHED = 'published',
    ARCHIVED = 'archived'
}

@Entity('news_articles')
export class NewsArticle {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ length: 500 })
    @Index('idx_news_article_title')
    title: string;

    @Column({ unique: true, length: 600 })
    @Index('idx_news_article_slug')
    slug: string; // Auto-generated from title

    @Column({ type: 'text', nullable: true })
    excerpt: string; // Short description

    @Column({ type: 'text' })
    content: string; // Full article content (HTML)

    @Column({ nullable: true, length: 1000 })
    featuredImage: string; // Main image URL

    @Column('simple-array', { nullable: true })
    tags: string[]; // ['messi', 'barcelona', 'transfer']

    // Status management
    @Column({
        type: 'enum',
        enum: ArticleStatus,
        default: ArticleStatus.DRAFT
    })
    @Index('idx_news_article_status')
    status: ArticleStatus;

    @Column({ nullable: true })
    @Index('idx_news_article_published_at')
    publishedAt?: Date;

    // SEO fields
    @Column({ nullable: true, length: 300 })
    metaTitle: string;

    @Column({ type: 'text', nullable: true })
    metaDescription: string;

    // Football-specific fields (relationships to existing entities)
    @Column({ nullable: true })
    @Index('idx_news_article_league')
    relatedLeagueId: number; // Link to League entity

    @Column({ nullable: true })
    @Index('idx_news_article_team')
    relatedTeamId: number; // Link to Team entity

    @Column({ nullable: true })
    @Index('idx_news_article_player')
    relatedPlayerId: number; // Link to Player entity

    @Column({ nullable: true })
    @Index('idx_news_article_fixture')
    relatedFixtureId: number; // Link to Fixture entity

    // Analytics
    @Column({ default: 0 })
    viewCount: number;

    @Column({ default: 0 })
    shareCount: number;

    @Column({ default: 0 })
    likeCount: number;

    // Featured article flag
    @Column({ default: false })
    @Index('idx_news_article_featured')
    isFeatured: boolean;

    // Priority for sorting (higher = more important)
    @Column({ default: 0 })
    @Index('idx_news_article_priority')
    priority: number;

    // Category relationship
    @ManyToOne(() => NewsCategory)
    @JoinColumn({ name: 'categoryId' })
    category: NewsCategory;

    @Column()
    @Index('idx_news_article_category')
    categoryId: number;

    // Audit fields
    @Column()
    authorId: number; // SystemUser who created

    @Column({ nullable: true })
    updatedBy: number; // SystemUser who last updated

    @CreateDateColumn()
    @Index('idx_news_article_created_at')
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    // Computed fields (for search and filtering)
    @Column({ type: 'tsvector', nullable: true })
    @Index('idx_news_article_search', { synchronize: false })
    searchVector: string; // Full-text search vector
}
