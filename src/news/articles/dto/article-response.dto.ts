import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsInt, IsString, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ArticleStatus } from '../entities/news-article.entity';
import { CategoryResponseDto } from '../../categories/dto';

// Related entities DTOs
export class RelatedLeagueDto {
    @ApiProperty({ description: 'League ID', example: 39 })
    id: number;

    @ApiProperty({ description: 'External API ID', example: 39 })
    externalId: number;

    @ApiProperty({ description: 'League name', example: 'Premier League' })
    name: string;

    @ApiProperty({ description: 'Country', example: 'England' })
    country: string;

    @ApiPropertyOptional({ description: 'League logo URL' })
    logo?: string;

    @ApiProperty({ description: 'Season', example: 2024 })
    season: number;
}

export class RelatedTeamDto {
    @ApiProperty({ description: 'Team ID', example: 50 })
    id: number;

    @ApiProperty({ description: 'External API ID', example: 50 })
    externalId: number;

    @ApiProperty({ description: 'Team name', example: 'Manchester City' })
    name: string;

    @ApiPropertyOptional({ description: 'Team code', example: 'MCI' })
    code?: string;

    @ApiPropertyOptional({ description: 'Country', example: 'England' })
    country?: string;

    @ApiPropertyOptional({ description: 'Team logo URL' })
    logo?: string;
}

export class RelatedPlayerDto {
    @ApiProperty({ description: 'Player ID', example: 154 })
    id: number;

    @ApiProperty({ description: 'External API ID', example: 154 })
    externalId: number;

    @ApiProperty({ description: 'Player name', example: 'Erling Haaland' })
    name: string;

    @ApiPropertyOptional({ description: 'Age', example: 24 })
    age?: number;

    @ApiPropertyOptional({ description: 'Nationality', example: 'Norway' })
    nationality?: string;

    @ApiPropertyOptional({ description: 'Player photo URL' })
    photo?: string;
}

export class RelatedFixtureDto {
    @ApiProperty({ description: 'Fixture ID', example: 1234567 })
    id: number;

    @ApiProperty({ description: 'External API ID', example: 1234567 })
    externalId: number;

    @ApiProperty({ description: 'League name', example: 'Premier League' })
    leagueName: string;

    @ApiProperty({ description: 'Home team vs Away team', example: 'Manchester City vs Liverpool' })
    matchTitle: string;

    @ApiProperty({ description: 'Match date' })
    date: Date;

    @ApiProperty({ description: 'Match status', example: 'FT' })
    status: string;
}

export class AuthorDto {
    @ApiProperty({ description: 'Author ID', example: 1 })
    id: number;

    @ApiProperty({ description: 'Username', example: 'admin' })
    username: string;

    @ApiPropertyOptional({ description: 'Full name', example: 'Admin User' })
    fullName?: string;

    @ApiProperty({ description: 'Role', example: 'admin' })
    role: string;
}

export class ArticleResponseDto {
    @ApiProperty({
        description: 'Article ID',
        example: 1
    })
    id: number;

    @ApiProperty({
        description: 'Article title',
        example: 'Messi Signs New Contract with Barcelona'
    })
    title: string;

    @ApiProperty({
        description: 'Article slug',
        example: 'messi-signs-new-contract-barcelona'
    })
    slug: string;

    @ApiPropertyOptional({
        description: 'Article excerpt',
        example: 'Lionel Messi has agreed to a new 3-year contract with Barcelona...'
    })
    excerpt?: string;

    @ApiProperty({
        description: 'Article content (HTML)',
        example: '<p>Lionel Messi has officially signed a new contract...</p>'
    })
    content: string;

    @ApiPropertyOptional({
        description: 'Featured image URL',
        example: 'https://example.com/images/messi-contract.jpg'
    })
    featuredImage?: string;

    @ApiPropertyOptional({
        description: 'Article tags',
        example: ['messi', 'barcelona', 'contract', 'transfer'],
        type: [String]
    })
    tags?: string[];

    @ApiProperty({
        description: 'Article status',
        example: ArticleStatus.PUBLISHED,
        enum: ArticleStatus
    })
    status: ArticleStatus;

    @ApiPropertyOptional({
        description: 'Published date',
        example: '2025-05-30T10:00:00.000Z'
    })
    publishedAt?: Date;

    @ApiPropertyOptional({
        description: 'SEO meta title',
        example: 'Messi Signs New Barcelona Contract - Latest Football News'
    })
    metaTitle?: string;

    @ApiPropertyOptional({
        description: 'SEO meta description',
        example: 'Breaking news: Lionel Messi has signed a new 3-year contract...'
    })
    metaDescription?: string;

    @ApiPropertyOptional({
        description: 'Related league ID',
        example: 39
    })
    relatedLeagueId?: number;

    @ApiPropertyOptional({
        description: 'Related league details',
        type: RelatedLeagueDto
    })
    relatedLeague?: RelatedLeagueDto;

    @ApiPropertyOptional({
        description: 'Related team ID',
        example: 529
    })
    relatedTeamId?: number;

    @ApiPropertyOptional({
        description: 'Related team details',
        type: RelatedTeamDto
    })
    relatedTeam?: RelatedTeamDto;

    @ApiPropertyOptional({
        description: 'Related player ID',
        example: 154
    })
    relatedPlayerId?: number;

    @ApiPropertyOptional({
        description: 'Related player details',
        type: RelatedPlayerDto
    })
    relatedPlayer?: RelatedPlayerDto;

    @ApiPropertyOptional({
        description: 'Related fixture ID',
        example: 1234567
    })
    relatedFixtureId?: number;

    @ApiPropertyOptional({
        description: 'Related fixture details',
        type: RelatedFixtureDto
    })
    relatedFixture?: RelatedFixtureDto;

    @ApiProperty({
        description: 'View count',
        example: 1250
    })
    viewCount: number;

    @ApiProperty({
        description: 'Share count',
        example: 45
    })
    shareCount: number;

    @ApiProperty({
        description: 'Like count',
        example: 89
    })
    likeCount: number;

    @ApiProperty({
        description: 'Featured status',
        example: false
    })
    isFeatured: boolean;

    @ApiProperty({
        description: 'Article priority',
        example: 0
    })
    priority: number;

    @ApiProperty({
        description: 'Category ID',
        example: 1
    })
    categoryId: number;

    @ApiProperty({
        description: 'Category information',
        type: CategoryResponseDto
    })
    category: CategoryResponseDto;

    @ApiProperty({
        description: 'Author ID',
        example: 1
    })
    authorId: number;

    @ApiPropertyOptional({
        description: 'Author details',
        type: AuthorDto
    })
    author?: AuthorDto;

    @ApiProperty({
        description: 'Creation timestamp',
        example: '2025-05-30T08:00:00.000Z'
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Last update timestamp',
        example: '2025-05-30T08:00:00.000Z'
    })
    updatedAt: Date;
}

// DTO for list/pagination responses (simplified)
export class ArticleListItemDto {
    @ApiProperty({
        description: 'Article ID',
        example: 1
    })
    id: number;

    @ApiProperty({
        description: 'Article title',
        example: 'Messi Signs New Contract with Barcelona'
    })
    title: string;

    @ApiProperty({
        description: 'Article slug',
        example: 'messi-signs-new-contract-barcelona'
    })
    slug: string;

    @ApiPropertyOptional({
        description: 'Article excerpt',
        example: 'Lionel Messi has agreed to a new 3-year contract with Barcelona...'
    })
    excerpt?: string;

    @ApiPropertyOptional({
        description: 'Featured image URL',
        example: 'https://example.com/images/messi-contract.jpg'
    })
    featuredImage?: string;

    @ApiPropertyOptional({
        description: 'Article tags',
        example: ['transfer', 'messi', 'barcelona']
    })
    tags?: string[];

    @ApiProperty({
        description: 'Article status',
        enum: ArticleStatus,
        example: ArticleStatus.PUBLISHED
    })
    status: ArticleStatus;

    @ApiPropertyOptional({
        description: 'Publication date',
        example: '2025-05-30T08:00:00.000Z'
    })
    publishedAt?: Date;

    @ApiPropertyOptional({
        description: 'Related league details',
        type: RelatedLeagueDto
    })
    relatedLeague?: RelatedLeagueDto;

    @ApiPropertyOptional({
        description: 'Related team details',
        type: RelatedTeamDto
    })
    relatedTeam?: RelatedTeamDto;

    @ApiPropertyOptional({
        description: 'Related player details',
        type: RelatedPlayerDto
    })
    relatedPlayer?: RelatedPlayerDto;

    @ApiPropertyOptional({
        description: 'Related fixture details',
        type: RelatedFixtureDto
    })
    relatedFixture?: RelatedFixtureDto;

    @ApiProperty({
        description: 'View count',
        example: 1250
    })
    viewCount: number;

    @ApiProperty({
        description: 'Share count',
        example: 45
    })
    shareCount: number;

    @ApiProperty({
        description: 'Like count',
        example: 89
    })
    likeCount: number;

    @ApiProperty({
        description: 'Featured status',
        example: false
    })
    isFeatured: boolean;

    @ApiProperty({
        description: 'Article priority',
        example: 0
    })
    priority: number;

    @ApiProperty({
        description: 'Category information',
        type: CategoryResponseDto
    })
    category: CategoryResponseDto;

    @ApiPropertyOptional({
        description: 'Author details',
        type: AuthorDto
    })
    author?: AuthorDto;

    @ApiProperty({
        description: 'Creation timestamp',
        example: '2025-05-30T08:00:00.000Z'
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Last update timestamp',
        example: '2025-05-30T08:00:00.000Z'
    })
    updatedAt: Date;
}

export class PaginatedArticlesResponseDto {
    @ApiProperty({
        description: 'Array of articles',
        type: [ArticleListItemDto]
    })
    data: ArticleListItemDto[];

    @ApiProperty({
        description: 'Pagination metadata',
        example: {
            totalItems: 25,
            totalPages: 3,
            currentPage: 1,
            limit: 10
        }
    })
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };

    @ApiProperty({
        description: 'HTTP status code',
        example: 200
    })
    status: number;
}

export class PublicArticlesDto {
    @ApiPropertyOptional({
        description: 'Page number for pagination',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Number of items per page',
        example: 10,
        minimum: 1,
        maximum: 50,
        default: 10
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(50)
    limit?: number = 10;

    @ApiPropertyOptional({
        description: 'Search query',
        example: 'messi barcelona'
    })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({
        description: 'Filter by tags',
        example: 'messi,barcelona'
    })
    @IsOptional()
    @IsString()
    tags?: string;

    @ApiPropertyOptional({
        description: 'Sort by field',
        example: 'publishedAt',
        enum: ['publishedAt', 'viewCount', 'priority'],
        default: 'publishedAt'
    })
    @IsOptional()
    sortBy?: 'publishedAt' | 'viewCount' | 'priority' = 'publishedAt';

    @ApiPropertyOptional({
        description: 'Sort direction',
        example: 'DESC',
        enum: ['ASC', 'DESC'],
        default: 'DESC'
    })
    @IsOptional()
    sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
