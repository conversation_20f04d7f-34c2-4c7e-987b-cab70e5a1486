import { IsOptional, IsInt, <PERSON><PERSON>tring, IsEnum, IsBoolean, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { ArticleStatus } from '../entities/news-article.entity';

export class GetArticlesDto {
    @ApiPropertyOptional({
        description: 'Page number for pagination',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Number of items per page',
        example: 10,
        minimum: 1,
        maximum: 100,
        default: 10
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    limit?: number = 10;

    @ApiPropertyOptional({
        description: 'Filter by article status',
        example: ArticleStatus.PUBLISHED,
        enum: ArticleStatus
    })
    @IsOptional()
    @IsEnum(ArticleStatus)
    status?: ArticleStatus;

    @ApiPropertyOptional({
        description: 'Filter by category ID',
        example: 1
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    categoryId?: number;

    @ApiPropertyOptional({
        description: 'Filter by category slug',
        example: 'transfer-news'
    })
    @IsOptional()
    @IsString()
    categorySlug?: string;

    @ApiPropertyOptional({
        description: 'Filter by author ID',
        example: 1
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    authorId?: number;

    @ApiPropertyOptional({
        description: 'Filter by featured articles only',
        example: true
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    isFeatured?: boolean;

    @ApiPropertyOptional({
        description: 'Filter by related league ID',
        example: 39
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    relatedLeagueId?: number;

    @ApiPropertyOptional({
        description: 'Filter by related team ID',
        example: 529
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    relatedTeamId?: number;

    @ApiPropertyOptional({
        description: 'Filter by related player ID',
        example: 154
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    relatedPlayerId?: number;

    @ApiPropertyOptional({
        description: 'Filter by related fixture ID',
        example: 1234567
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    relatedFixtureId?: number;

    @ApiPropertyOptional({
        description: 'Search query (full-text search)',
        example: 'messi barcelona transfer'
    })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({
        description: 'Filter by tags (comma-separated)',
        example: 'messi,barcelona,transfer'
    })
    @IsOptional()
    @IsString()
    tags?: string;

    @ApiPropertyOptional({
        description: 'Sort by field',
        example: 'createdAt',
        enum: ['createdAt', 'updatedAt', 'publishedAt', 'title', 'priority', 'viewCount'],
        default: 'createdAt'
    })
    @IsOptional()
    sortBy?: 'createdAt' | 'updatedAt' | 'publishedAt' | 'title' | 'priority' | 'viewCount' = 'createdAt';

    @ApiPropertyOptional({
        description: 'Sort direction',
        example: 'DESC',
        enum: ['ASC', 'DESC'],
        default: 'DESC'
    })
    @IsOptional()
    sortOrder?: 'ASC' | 'DESC' = 'DESC';

    @ApiPropertyOptional({
        description: 'Date range start (ISO string)',
        example: '2025-01-01T00:00:00.000Z'
    })
    @IsOptional()
    @IsString()
    dateFrom?: string;

    @ApiPropertyOptional({
        description: 'Date range end (ISO string)',
        example: '2025-12-31T23:59:59.999Z'
    })
    @IsOptional()
    @IsString()
    dateTo?: string;
}
