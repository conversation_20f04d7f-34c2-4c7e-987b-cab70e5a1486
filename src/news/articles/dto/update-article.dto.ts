import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateArticleDto } from './create-article.dto';
import { IsOptional, IsDate } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class UpdateArticleDto extends PartialType(
    OmitType(CreateArticleDto, ['slug'] as const)
) {
    // Slug cannot be updated after creation for URL stability
    // All other fields from CreateArticleDto are optional for updates

    @ApiPropertyOptional({
        description: 'Published date (set automatically when status changes to published)',
        example: '2025-06-02T10:00:00.000Z'
    })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    publishedAt?: Date;
}
