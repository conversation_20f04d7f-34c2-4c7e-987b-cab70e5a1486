import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CategoryResponseDto {
    @ApiProperty({
        description: 'Category ID',
        example: 1
    })
    id: number;

    @ApiProperty({
        description: 'Category slug',
        example: 'transfer-news'
    })
    slug: string;

    @ApiProperty({
        description: 'Category name',
        example: 'Transfer News'
    })
    name: string;

    @ApiPropertyOptional({
        description: 'Category description',
        example: 'Latest transfer rumors, confirmations and market updates'
    })
    description?: string;

    @ApiPropertyOptional({
        description: 'Icon identifier',
        example: 'transfer'
    })
    icon?: string;

    @ApiPropertyOptional({
        description: 'Hex color code',
        example: '#FF6B35'
    })
    color?: string;

    @ApiProperty({
        description: 'Sort order',
        example: 1
    })
    sortOrder: number;

    @ApiProperty({
        description: 'Active status',
        example: true
    })
    isActive: boolean;

    @ApiProperty({
        description: 'Public visibility',
        example: true
    })
    isPublic: boolean;

    @ApiPropertyOptional({
        description: 'SEO meta title',
        example: 'Transfer News - Latest Football Transfers'
    })
    metaTitle?: string;

    @ApiPropertyOptional({
        description: 'SEO meta description',
        example: 'Stay updated with the latest football transfer news...'
    })
    metaDescription?: string;

    @ApiProperty({
        description: 'Total articles in category',
        example: 25
    })
    articleCount: number;

    @ApiProperty({
        description: 'Published articles in category',
        example: 20
    })
    publishedArticleCount: number;

    @ApiProperty({
        description: 'Creation timestamp',
        example: '2025-05-30T08:00:00.000Z'
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Last update timestamp',
        example: '2025-05-30T08:00:00.000Z'
    })
    updatedAt: Date;
}

export class PaginatedCategoriesResponseDto {
    @ApiProperty({
        description: 'Array of categories',
        type: [CategoryResponseDto]
    })
    data: CategoryResponseDto[];

    @ApiProperty({
        description: 'Pagination metadata',
        example: {
            totalItems: 8,
            totalPages: 1,
            currentPage: 1,
            limit: 10
        }
    })
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };

    @ApiProperty({
        description: 'HTTP status code',
        example: 200
    })
    status: number;
}

export class CategoryOrderDto {
    @ApiProperty({
        description: 'Category ID',
        example: 1
    })
    id: number;

    @ApiProperty({
        description: 'New sort order',
        example: 5
    })
    sortOrder: number;
}
