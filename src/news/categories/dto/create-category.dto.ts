import { IsString, IsOptional, IsBoolean, IsInt, IsHexColor, Length, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCategoryDto {
    @ApiProperty({
        description: 'Category slug (URL-friendly identifier)',
        example: 'transfer-news',
        minLength: 2,
        maxLength: 100
    })
    @IsString()
    @Length(2, 100)
    slug: string;

    @ApiProperty({
        description: 'Category display name',
        example: 'Transfer News',
        minLength: 2,
        maxLength: 200
    })
    @IsString()
    @Length(2, 200)
    name: string;

    @ApiPropertyOptional({
        description: 'Category description',
        example: 'Latest transfer rumors, confirmations and market updates'
    })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiPropertyOptional({
        description: 'Icon identifier or URL',
        example: 'transfer',
        maxLength: 500
    })
    @IsOptional()
    @IsString()
    @Length(1, 500)
    icon?: string;

    @ApiPropertyOptional({
        description: 'Hex color code for UI theming',
        example: '#FF6B35'
    })
    @IsOptional()
    @IsHexColor()
    color?: string;

    @ApiPropertyOptional({
        description: 'Display sort order (lower numbers appear first)',
        example: 1,
        minimum: 0,
        maximum: 9999
    })
    @IsOptional()
    @IsInt()
    @Min(0)
    @Max(9999)
    sortOrder?: number;

    @ApiPropertyOptional({
        description: 'Whether category is active',
        example: true,
        default: true
    })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional({
        description: 'Whether category is visible to public',
        example: true,
        default: true
    })
    @IsOptional()
    @IsBoolean()
    isPublic?: boolean;

    @ApiPropertyOptional({
        description: 'SEO meta title',
        example: 'Transfer News - Latest Football Transfers',
        maxLength: 300
    })
    @IsOptional()
    @IsString()
    @Length(1, 300)
    metaTitle?: string;

    @ApiPropertyOptional({
        description: 'SEO meta description',
        example: 'Stay updated with the latest football transfer news, rumors, and confirmed deals from top leagues worldwide.'
    })
    @IsOptional()
    @IsString()
    metaDescription?: string;
}
