import { IsOptional, IsBoolean, <PERSON>I<PERSON>, <PERSON>, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class GetCategoriesDto {
    @ApiPropertyOptional({
        description: 'Page number for pagination',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Number of items per page',
        example: 10,
        minimum: 1,
        maximum: 100,
        default: 10
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    limit?: number = 10;

    @ApiPropertyOptional({
        description: 'Filter by active status',
        example: true
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional({
        description: 'Filter by public visibility',
        example: true
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    isPublic?: boolean;

    @ApiPropertyOptional({
        description: 'Sort by field',
        example: 'sortOrder',
        enum: ['sortOrder', 'name', 'createdAt', 'updatedAt', 'articleCount'],
        default: 'sortOrder'
    })
    @IsOptional()
    sortBy?: 'sortOrder' | 'name' | 'createdAt' | 'updatedAt' | 'articleCount' = 'sortOrder';

    @ApiPropertyOptional({
        description: 'Sort direction',
        example: 'ASC',
        enum: ['ASC', 'DESC'],
        default: 'ASC'
    })
    @IsOptional()
    sortOrder?: 'ASC' | 'DESC' = 'ASC';
}
