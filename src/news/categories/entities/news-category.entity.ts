import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('news_categories')
export class NewsCategory {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true, length: 100 })
    @Index('idx_news_category_slug')
    slug: string; // 'transfer-news', 'match-reports', etc.

    @Column({ length: 200 })
    name: string; // 'Transfer News', 'Match Reports'

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ nullable: true, length: 500 })
    icon: string; // Icon URL or name

    @Column({ nullable: true, length: 7 })
    color: string; // Hex color for UI (#FF6B35)

    @Column({ default: 0 })
    @Index('idx_news_category_sort_order')
    sortOrder: number; // Display order

    @Column({ default: true })
    @Index('idx_news_category_active')
    isActive: boolean;

    @Column({ default: true })
    @Index('idx_news_category_public')
    isPublic: boolean; // Show on public site

    // SEO fields
    @Column({ nullable: true, length: 300 })
    metaTitle: string;

    @Column({ type: 'text', nullable: true })
    metaDescription: string;

    // Statistics (will be updated by triggers/jobs)
    @Column({ default: 0 })
    articleCount: number; // Total articles in this category

    @Column({ default: 0 })
    publishedArticleCount: number; // Published articles only

    // Audit fields
    @Column()
    createdBy: number; // SystemUser ID

    @Column({ nullable: true })
    updatedBy: number; // SystemUser ID

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
