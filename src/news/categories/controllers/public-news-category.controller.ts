import { Controller, Get, Param } from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiParam,
} from '@nestjs/swagger';
import { NewsCategoryService } from '../services/news-category.service';
import { Public } from '../../../auth/core/decorators/auth.decorators';
import { CategoryResponseDto } from '../dto';

@ApiTags('Public - News Categories')
@Controller('news/categories')
export class PublicNewsCategoryController {
    constructor(private readonly categoryService: NewsCategoryService) { }

    @ApiOperation({
        summary: 'Get public categories',
        description: 'Get all active and public news categories, sorted by display order.',
    })
    @ApiResponse({
        status: 200,
        description: 'Public categories retrieved successfully',
        type: [CategoryResponseDto],
        examples: {
            success: {
                summary: 'Successful response',
                value: [
                    {
                        id: 1,
                        slug: 'transfer-news',
                        name: 'Transfer News',
                        description: 'Latest transfer rumors, confirmations and market updates',
                        icon: 'transfer',
                        color: '#FF6B35',
                        sortOrder: 1,
                        isActive: true,
                        isPublic: true,
                        metaTitle: 'Transfer News - Latest Football Transfers',
                        metaDescription: 'Stay updated with the latest football transfer news...',
                        articleCount: 25,
                        publishedArticleCount: 20,
                        createdAt: '2025-05-30T08:00:00.000Z',
                        updatedAt: '2025-05-30T08:00:00.000Z',
                    },
                    {
                        id: 2,
                        slug: 'match-reports',
                        name: 'Match Reports',
                        description: 'Detailed analysis and reports from recent matches',
                        icon: 'match',
                        color: '#4ECDC4',
                        sortOrder: 2,
                        isActive: true,
                        isPublic: true,
                        metaTitle: 'Match Reports - Football Analysis',
                        metaDescription: 'Read detailed match reports and analysis...',
                        articleCount: 18,
                        publishedArticleCount: 15,
                        createdAt: '2025-05-30T08:00:00.000Z',
                        updatedAt: '2025-05-30T08:00:00.000Z',
                    },
                ],
            },
        },
    })
    @Public()
    @Get()
    async getPublicCategories(): Promise<CategoryResponseDto[]> {
        return this.categoryService.getPublicCategories();
    }

    @ApiOperation({
        summary: 'Get category by slug',
        description: 'Get a specific news category by its slug (URL-friendly identifier).',
    })
    @ApiParam({
        name: 'slug',
        description: 'Category slug',
        example: 'transfer-news',
    })
    @ApiResponse({
        status: 200,
        description: 'Category retrieved successfully',
        type: CategoryResponseDto,
        examples: {
            success: {
                summary: 'Successful response',
                value: {
                    id: 1,
                    slug: 'transfer-news',
                    name: 'Transfer News',
                    description: 'Latest transfer rumors, confirmations and market updates',
                    icon: 'transfer',
                    color: '#FF6B35',
                    sortOrder: 1,
                    isActive: true,
                    isPublic: true,
                    metaTitle: 'Transfer News - Latest Football Transfers',
                    metaDescription: 'Stay updated with the latest football transfer news, rumors, and confirmed deals from top leagues worldwide.',
                    articleCount: 25,
                    publishedArticleCount: 20,
                    createdAt: '2025-05-30T08:00:00.000Z',
                    updatedAt: '2025-05-30T08:00:00.000Z',
                },
            },
        },
    })
    @ApiResponse({
        status: 404,
        description: 'Category not found',
        example: {
            message: "Category with slug 'invalid-slug' not found",
            error: 'Not Found',
            statusCode: 404,
        },
    })
    @Public()
    @Get(':slug')
    async getCategoryBySlug(@Param('slug') slug: string): Promise<CategoryResponseDto> {
        return this.categoryService.getCategoryBySlug(slug);
    }
}
