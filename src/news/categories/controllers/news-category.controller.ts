import {
    Controller,
    Get,
    Post,
    Patch,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    ParseIntPipe,
    HttpCode,
    HttpStatus,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
    ApiParam,
    ApiBody,
} from '@nestjs/swagger';
import { NewsCategoryService } from '../services/news-category.service';
import { SystemJwtAuthGuard } from '../../../auth/system/guards/system-jwt-auth.guard';
import { GetCurrentUser } from '../../../auth/core/decorators/auth.decorators';
import { SystemUser } from '../../../auth/system/entities/system-user.entity';
import {
    CreateCategoryDto,
    UpdateCategoryDto,
    GetCategoriesDto,
    CategoryResponseDto,
    PaginatedCategoriesResponseDto,
    CategoryOrderDto,
} from '../dto';

@ApiTags('Admin - News Categories')
@Controller('admin/news/categories')
@UseGuards(SystemJwtAuthGuard)
@ApiBearerAuth()
export class NewsCategoryController {
    constructor(private readonly categoryService: NewsCategoryService) { }

    @ApiOperation({
        summary: 'Create news category',
        description: 'Create a new news category. Requires admin/moderator/editor role.',
    })
    @ApiResponse({
        status: 201,
        description: 'Category created successfully',
        type: CategoryResponseDto,
    })
    @ApiResponse({
        status: 409,
        description: 'Category with slug already exists',
        example: {
            message: "Category with slug 'transfer-news' already exists",
            error: 'Conflict',
            statusCode: 409,
        },
    })
    @Post()
    async createCategory(
        @Body() createCategoryDto: CreateCategoryDto,
        @GetCurrentUser() user: SystemUser,
    ): Promise<CategoryResponseDto> {
        return this.categoryService.createCategory(createCategoryDto, user.id);
    }

    @ApiOperation({
        summary: 'Get all categories',
        description: 'Get paginated list of news categories with filtering options.',
    })
    @ApiResponse({
        status: 200,
        description: 'Categories retrieved successfully',
        type: PaginatedCategoriesResponseDto,
    })
    @Get()
    async getCategories(
        @Query() query: GetCategoriesDto,
    ): Promise<PaginatedCategoriesResponseDto> {
        return this.categoryService.getCategories(query);
    }

    @ApiOperation({
        summary: 'Get category by ID',
        description: 'Get a specific news category by its ID.',
    })
    @ApiParam({
        name: 'id',
        description: 'Category ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Category retrieved successfully',
        type: CategoryResponseDto,
    })
    @ApiResponse({
        status: 404,
        description: 'Category not found',
        example: {
            message: 'Category with ID 1 not found',
            error: 'Not Found',
            statusCode: 404,
        },
    })
    @Get(':id')
    async getCategoryById(
        @Param('id', ParseIntPipe) id: number,
    ): Promise<CategoryResponseDto> {
        return this.categoryService.getCategoryById(id);
    }

    @ApiOperation({
        summary: 'Update category',
        description: 'Update an existing news category. Slug cannot be changed.',
    })
    @ApiParam({
        name: 'id',
        description: 'Category ID',
        example: 1,
    })
    @ApiResponse({
        status: 200,
        description: 'Category updated successfully',
        type: CategoryResponseDto,
    })
    @ApiResponse({
        status: 404,
        description: 'Category not found',
    })
    @Patch(':id')
    async updateCategory(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateCategoryDto: UpdateCategoryDto,
        @GetCurrentUser() user: SystemUser,
    ): Promise<CategoryResponseDto> {
        return this.categoryService.updateCategory(id, updateCategoryDto, user.id);
    }

    @ApiOperation({
        summary: 'Delete category',
        description: 'Delete a news category. Warning: This will affect associated articles.',
    })
    @ApiParam({
        name: 'id',
        description: 'Category ID',
        example: 1,
    })
    @ApiResponse({
        status: 204,
        description: 'Category deleted successfully',
    })
    @ApiResponse({
        status: 404,
        description: 'Category not found',
    })
    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async deleteCategory(@Param('id', ParseIntPipe) id: number): Promise<void> {
        return this.categoryService.deleteCategory(id);
    }

    @ApiOperation({
        summary: 'Reorder categories',
        description: 'Update the sort order of multiple categories at once.',
    })
    @ApiBody({
        description: 'Array of category ID and sort order pairs',
        type: [CategoryOrderDto],
        examples: {
            reorder: {
                summary: 'Reorder example',
                value: [
                    { id: 1, sortOrder: 3 },
                    { id: 2, sortOrder: 1 },
                    { id: 3, sortOrder: 2 },
                ],
            },
        },
    })
    @ApiResponse({
        status: 200,
        description: 'Categories reordered successfully',
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid category IDs provided',
    })
    @Post('reorder')
    @HttpCode(HttpStatus.OK)
    async reorderCategories(@Body() orders: CategoryOrderDto[]): Promise<{ message: string }> {
        await this.categoryService.reorderCategories(orders);
        return { message: 'Categories reordered successfully' };
    }

    @ApiOperation({
        summary: 'Toggle category status',
        description: 'Activate or deactivate a category.',
    })
    @ApiParam({
        name: 'id',
        description: 'Category ID',
        example: 1,
    })
    @ApiBody({
        description: 'New active status',
        schema: {
            type: 'object',
            properties: {
                isActive: {
                    type: 'boolean',
                    example: true,
                },
            },
            required: ['isActive'],
        },
    })
    @ApiResponse({
        status: 200,
        description: 'Category status updated successfully',
        type: CategoryResponseDto,
    })
    @Post(':id/toggle-status')
    async toggleCategoryStatus(
        @Param('id', ParseIntPipe) id: number,
        @Body('isActive') isActive: boolean,
    ): Promise<CategoryResponseDto> {
        return this.categoryService.toggleCategoryStatus(id, isActive);
    }
}
