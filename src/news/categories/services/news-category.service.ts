import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NewsCategory } from '../entities/news-category.entity';
import { CacheService } from '../../../core/cache/cache.service';
import {
    CreateCategoryDto,
    UpdateCategoryDto,
    GetCategoriesDto,
    CategoryResponseDto,
    PaginatedCategoriesResponseDto,
    CategoryOrderDto
} from '../dto';

@Injectable()
export class NewsCategoryService {
    private readonly logger = new Logger(NewsCategoryService.name);
    private readonly CACHE_TTL = 3600; // 1 hour
    private readonly CACHE_PREFIX = 'news_category';

    constructor(
        @InjectRepository(NewsCategory)
        private readonly categoryRepository: Repository<NewsCategory>,
        private readonly cacheService: CacheService,
    ) {}

    /**
     * Create a new news category
     */
    async createCategory(dto: CreateCategoryDto, userId: number): Promise<CategoryResponseDto> {
        this.logger.debug(`Creating category with slug: ${dto.slug}`);

        // Check if slug already exists
        const existingCategory = await this.categoryRepository.findOne({
            where: { slug: dto.slug }
        });

        if (existingCategory) {
            throw new ConflictException(`Category with slug '${dto.slug}' already exists`);
        }

        // Create new category
        const category = this.categoryRepository.create({
            ...dto,
            createdBy: userId,
        });

        const savedCategory = await this.categoryRepository.save(category);

        // Clear cache
        await this.clearCategoryCache();

        this.logger.log(`Category created: ${savedCategory.slug} (ID: ${savedCategory.id})`);
        return this.mapToResponseDto(savedCategory);
    }

    /**
     * Update an existing category
     */
    async updateCategory(id: number, dto: UpdateCategoryDto, userId: number): Promise<CategoryResponseDto> {
        this.logger.debug(`Updating category ID: ${id}`);

        const category = await this.categoryRepository.findOne({ where: { id } });
        if (!category) {
            throw new NotFoundException(`Category with ID ${id} not found`);
        }

        // Update category
        Object.assign(category, dto, { updatedBy: userId });
        const updatedCategory = await this.categoryRepository.save(category);

        // Clear cache
        await this.clearCategoryCache();

        this.logger.log(`Category updated: ${updatedCategory.slug} (ID: ${updatedCategory.id})`);
        return this.mapToResponseDto(updatedCategory);
    }

    /**
     * Delete a category
     */
    async deleteCategory(id: number): Promise<void> {
        this.logger.debug(`Deleting category ID: ${id}`);

        const category = await this.categoryRepository.findOne({ where: { id } });
        if (!category) {
            throw new NotFoundException(`Category with ID ${id} not found`);
        }

        // TODO: Check if category has articles before deletion
        // For now, we'll allow deletion (articles will need to be reassigned)

        await this.categoryRepository.remove(category);

        // Clear cache
        await this.clearCategoryCache();

        this.logger.log(`Category deleted: ${category.slug} (ID: ${id})`);
    }

    /**
     * Get categories with pagination and filtering
     */
    async getCategories(query: GetCategoriesDto): Promise<PaginatedCategoriesResponseDto> {
        const { page = 1, limit = 10, isActive, isPublic, sortBy = 'sortOrder', sortOrder = 'ASC' } = query;

        // Try cache first
        const cacheKey = `${this.CACHE_PREFIX}_list_${JSON.stringify(query)}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for categories list: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // Build query
        const queryBuilder = this.categoryRepository.createQueryBuilder('category');

        // Apply filters
        if (isActive !== undefined) {
            queryBuilder.andWhere('category.isActive = :isActive', { isActive });
        }
        if (isPublic !== undefined) {
            queryBuilder.andWhere('category.isPublic = :isPublic', { isPublic });
        }

        // Apply sorting
        queryBuilder.orderBy(`category.${sortBy}`, sortOrder);

        // Apply pagination
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);

        // Execute query
        const [categories, totalItems] = await queryBuilder.getManyAndCount();

        const response: PaginatedCategoriesResponseDto = {
            data: categories.map(category => this.mapToResponseDto(category)),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);
        this.logger.debug(`Cached categories list: ${cacheKey} (TTL: ${this.CACHE_TTL}s)`);

        return response;
    }

    /**
     * Get category by ID
     */
    async getCategoryById(id: number): Promise<CategoryResponseDto> {
        const cacheKey = `${this.CACHE_PREFIX}_id_${id}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for category ID: ${id}`);
            return JSON.parse(cached);
        }

        const category = await this.categoryRepository.findOne({ where: { id } });
        if (!category) {
            throw new NotFoundException(`Category with ID ${id} not found`);
        }

        const response = this.mapToResponseDto(category);

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Get category by slug
     */
    async getCategoryBySlug(slug: string): Promise<CategoryResponseDto> {
        const cacheKey = `${this.CACHE_PREFIX}_slug_${slug}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for category slug: ${slug}`);
            return JSON.parse(cached);
        }

        const category = await this.categoryRepository.findOne({ where: { slug } });
        if (!category) {
            throw new NotFoundException(`Category with slug '${slug}' not found`);
        }

        const response = this.mapToResponseDto(category);

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Get public categories (active and public only)
     */
    async getPublicCategories(): Promise<CategoryResponseDto[]> {
        const cacheKey = `${this.CACHE_PREFIX}_public`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Cache hit for public categories`);
            return JSON.parse(cached);
        }

        const categories = await this.categoryRepository.find({
            where: { isActive: true, isPublic: true },
            order: { sortOrder: 'ASC' },
        });

        const response = categories.map(category => this.mapToResponseDto(category));

        // Cache result
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), this.CACHE_TTL);

        return response;
    }

    /**
     * Reorder categories
     */
    async reorderCategories(orders: CategoryOrderDto[]): Promise<void> {
        this.logger.debug(`Reordering ${orders.length} categories`);

        // Validate all category IDs exist
        const categoryIds = orders.map(order => order.id);
        const categories = await this.categoryRepository.findByIds(categoryIds);
        
        if (categories.length !== categoryIds.length) {
            throw new BadRequestException('One or more category IDs not found');
        }

        // Update sort orders
        for (const order of orders) {
            await this.categoryRepository.update(order.id, { sortOrder: order.sortOrder });
        }

        // Clear cache
        await this.clearCategoryCache();

        this.logger.log(`Reordered ${orders.length} categories`);
    }

    /**
     * Toggle category active status
     */
    async toggleCategoryStatus(id: number, isActive: boolean): Promise<CategoryResponseDto> {
        const category = await this.categoryRepository.findOne({ where: { id } });
        if (!category) {
            throw new NotFoundException(`Category with ID ${id} not found`);
        }

        category.isActive = isActive;
        const updatedCategory = await this.categoryRepository.save(category);

        // Clear cache
        await this.clearCategoryCache();

        this.logger.log(`Category ${isActive ? 'activated' : 'deactivated'}: ${category.slug}`);
        return this.mapToResponseDto(updatedCategory);
    }

    /**
     * Map entity to response DTO
     */
    private mapToResponseDto(category: NewsCategory): CategoryResponseDto {
        return {
            id: category.id,
            slug: category.slug,
            name: category.name,
            description: category.description,
            icon: category.icon,
            color: category.color,
            sortOrder: category.sortOrder,
            isActive: category.isActive,
            isPublic: category.isPublic,
            metaTitle: category.metaTitle,
            metaDescription: category.metaDescription,
            articleCount: category.articleCount,
            publishedArticleCount: category.publishedArticleCount,
            createdAt: category.createdAt,
            updatedAt: category.updatedAt,
        };
    }

    /**
     * Clear all category-related cache
     */
    private async clearCategoryCache(): Promise<void> {
        await this.cacheService.deleteByPattern(`${this.CACHE_PREFIX}_*`);
        this.logger.debug('Category cache cleared');
    }
}
