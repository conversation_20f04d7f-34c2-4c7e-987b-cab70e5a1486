import { NestFactory } from '@nestjs/core';
import { WorkerSyncModule } from './worker-sync.module';

async function bootstrap() {
    console.log(`[DEBUG] 🚀 Starting worker bootstrap...`);

    try {
        console.log(`[DEBUG] 📦 Creating NestFactory with WorkerSyncModule...`);
        const app = await NestFactory.create(WorkerSyncModule, {
            // ✅ MINIMAL WORKER LOGGING: Only errors in production
            logger: process.env.NODE_ENV === 'production'
                ? ['error'] // Only errors in production
                : ['log', 'error', 'warn'], // More logs in development
        });
        console.log(`[DEBUG] ✅ NestFactory created successfully`);

        console.log(`[DEBUG] 🔧 Initializing app...`);
        await app.init();
        console.log(`[DEBUG] ✅ App initialized successfully`);

        // ✅ CONDITIONAL STARTUP LOG: Only in development
        console.log(`[${new Date().toISOString()}] AutoUpdateSportsGame worker started (NODE_ENV: ${process.env.NODE_ENV})`);

        if (process.env.NODE_ENV !== 'production') {
            console.log(`[${new Date().toISOString()}] Worker PID: ${process.pid}`);
            console.log(`[${new Date().toISOString()}] Cronjobs should start automatically...`);
        }

        console.log(`[DEBUG] 🎯 Worker bootstrap completed!`);

        // Debug: Check if SyncService is loaded
        // try {
        //     const syncService = app.get('SyncService', { strict: false });
        //     console.log(`[DEBUG] 🔍 SyncService found:`, !!syncService);
        // } catch (e) {
        //     console.log(`[DEBUG] ❌ SyncService not found:`, e.message);
        // }

    } catch (error) {
        console.error(`[DEBUG] ❌ Bootstrap failed:`, error);
        throw error;
    }
}

bootstrap();