import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>tring, IsBoolean, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GetStandingsDto {
      @ApiPropertyOptional({
            description: 'League external ID',
            example: 39,
            type: Number
      })
      @IsOptional()
      @Transform(({ value }) => parseInt(value))
      @IsNumber()
      league?: number;

      @ApiPropertyOptional({
            description: 'Season year',
            example: 2024,
            type: Number
      })
      @IsOptional()
      @Transform(({ value }) => parseInt(value))
      @IsNumber()
      season?: number;

      @ApiPropertyOptional({
            description: 'Team external ID',
            example: 33,
            type: Number
      })
      @IsOptional()
      @Transform(({ value }) => parseInt(value))
      @IsNumber()
      team?: number;

      @ApiPropertyOptional({
            description: 'Page number for pagination',
            example: 1,
            type: Number
      })
      @IsOptional()
      @Transform(({ value }) => parseInt(value))
      @IsNumber()
      page?: number;

      @ApiPropertyOptional({
            description: 'Items per page',
            example: 20,
            type: Number
      })
      @IsOptional()
      @Transform(({ value }) => parseInt(value))
      @IsNumber()
      limit?: number;

      @ApiPropertyOptional({
            description: 'Force fetch from API (bypass cache)',
            example: false,
            type: Boolean
      })
      @IsOptional()
      @Transform(({ value }) => value === 'true' || value === true)
      @IsBoolean()
      newdb?: boolean;

      @ApiPropertyOptional({
            description: 'Response format: "internal" (flat array) or "external" (nested like api-sports.io)',
            example: 'internal',
            type: String,
            enum: ['internal', 'external']
      })
      @IsOptional()
      @IsString()
      format?: 'internal' | 'external';
}

export class StandingResponseDto {
      @ApiProperty({ description: 'Standing ID', example: 1 })
      id: number;

      @ApiProperty({ description: 'League external ID', example: 39 })
      leagueId: number;

      @ApiProperty({ description: 'League name', example: 'Premier League' })
      leagueName: string;

      @ApiProperty({ description: 'Season year', example: 2024 })
      season: number;

      @ApiProperty({ description: 'Team external ID', example: 33 })
      teamId: number;

      @ApiProperty({ description: 'Team name', example: 'Manchester United' })
      teamName: string;

      @ApiProperty({ description: 'Team logo path', example: '/images/teams/33.png' })
      teamLogo: string;

      @ApiProperty({ description: 'League position', example: 1 })
      position: number;

      @ApiProperty({ description: 'Total points', example: 75 })
      points: number;

      @ApiProperty({ description: 'Games played', example: 30 })
      played: number;

      @ApiProperty({ description: 'Games won', example: 22 })
      win: number;

      @ApiProperty({ description: 'Games drawn', example: 9 })
      draw: number;

      @ApiProperty({ description: 'Games lost', example: 1 })
      lose: number;

      @ApiProperty({ description: 'Goals scored', example: 65 })
      goalsFor: number;

      @ApiProperty({ description: 'Goals conceded', example: 20 })
      goalsAgainst: number;

      @ApiProperty({ description: 'Goal difference', example: 45 })
      goalsDiff: number;

      @ApiProperty({ description: 'Recent form', example: 'WWWDW' })
      form: string;

      @ApiProperty({ description: 'Group name (for tournaments)', example: 'Group A', required: false })
      groupName?: string;
}

export class PaginatedStandingsResponse {
      @ApiProperty({
            description: 'Array of standings',
            type: [StandingResponseDto]
      })
      data: StandingResponseDto[];

      @ApiProperty({
            description: 'Pagination metadata',
            example: {
                  totalItems: 20,
                  totalPages: 1,
                  currentPage: 1,
                  limit: 20
            }
      })
      meta: {
            totalItems: number;
            totalPages: number;
            currentPage: number;
            limit: number;
      };

      @ApiProperty({ description: 'HTTP status code', example: 200 })
      status: number;
}
