import { IsInt, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsBoolean, IsNotEmpty, IsObject } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/** Interface for season detail */
interface SeasonDetail {
    year: number;
    start: string;
    end: string;
    current: boolean;
    coverage: {
        fixtures: boolean;
        standings: boolean;
        players: boolean;
        top_scorers: boolean;
        top_assists: boolean;
        top_cards: boolean;
        injuries: boolean;
        predictions: boolean;
        odds: boolean;
    };
}


/** DTO for creating a league */
export class CreateLeagueDto {
    @Type(() => Number)
    @IsInt()
    @Min(1)
    externalId: number;

    @IsString()
    @IsNotEmpty()
    name: string;

    @IsString()
    @IsNotEmpty()
    country: string;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    season: number;

    @IsString()
    @IsOptional()
    logo?: string;

    @IsString()
    @IsOptional()
    flag?: string;

    @Transform(({ value }) => value === 'true')
    @IsBoolean({ message: 'Active must be a boolean' })
    @IsOptional()
    active?: boolean;

    @IsObject()
    @IsOptional()
    season_detail?: SeasonDetail;

    @IsString()
    type: string;

    @Transform(({ value }) => value === 'true')
    @IsBoolean({ message: 'IsHot must be a boolean' })
    @IsOptional()
    isHot?: boolean;
}

/** DTO for updating a league */
export class UpdateLeagueDto {
    @IsString()
    @IsOptional()
    name?: string;

    @IsString()
    @IsOptional()
    country?: string;

    @IsString()
    @IsOptional()
    logo?: string;

    @IsString()
    @IsOptional()
    flag?: string;

    @Transform(({ value }) => {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') return value === 'true';
        return value;
    })
    @IsBoolean({ message: 'Active must be a boolean' })
    @IsOptional()
    active?: boolean;

    @IsObject()
    @IsOptional()
    season_detail?: SeasonDetail;

    @IsString()
    @IsOptional()
    type?: string;

    @Transform(({ value }) => {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') return value === 'true';
        return value;
    })
    @IsBoolean({ message: 'IsHot must be a boolean' })
    @IsOptional()
    isHot?: boolean;
}
/** DTO for querying leagues */
export class GetLeaguesDto {
    @Type(() => Number)
    @IsInt()
    @IsOptional()
    season?: number;

    @IsString()
    @IsOptional()
    country?: string;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    page: number = 1;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    @IsOptional()
    limit: number = 10;

    @Transform(({ value }) => value === 'true')
    @IsBoolean({ message: 'Active must be a boolean' })
    @IsOptional()
    active?: boolean;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    newdb?: boolean;

    @IsString()
    @IsOptional()
    type?: string;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    team?: number;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    league?: number;


    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    last?: number;

    @Transform(({ value }) => value === 'true')
    @IsBoolean({ message: 'IsHot must be a boolean' })
    @IsOptional()
    isHot?: boolean;

    @IsString()
    @IsOptional()
    search?: string;
}

/** DTO for league response */
export class LeagueResponseDto {
    @IsInt()
    id: number;

    @IsInt()
    externalId: number;

    @IsString()
    name: string;

    @IsString()
    country: string;

    @IsString()
    @IsOptional()
    logo?: string;

    @IsString()
    @IsOptional()
    flag?: string;

    @IsInt()
    season: number;

    @Transform(({ value }) => value === 'true')
    @IsBoolean({ message: 'Active must be a boolean' })
    @IsOptional()
    active?: boolean;

    @IsObject()
    @IsOptional()
    season_detail?: SeasonDetail;

    @IsString()
    type: string;

    @Transform(({ value }) => value === 'true')
    @IsBoolean({ message: 'IsHot must be a boolean' })
    @IsOptional()
    isHot?: boolean;
}

/** Paginated response for leagues */
export interface PaginatedLeaguesResponse {
    data: LeagueResponseDto[];
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };
    status: number;
}