import { ApiProperty } from '@nestjs/swagger';

// External API compatible DTO for standings
export class StandingTeamDto {
      @ApiProperty({ description: 'Team external ID', example: 8177 })
      id: number;

      @ApiProperty({ description: 'Team name', example: 'Denmark U21' })
      name: string;

      @ApiProperty({ description: 'Team logo URL', example: 'https://media.api-sports.io/football/teams/8177.png' })
      logo: string;
}

export class StandingGoalsDto {
      @ApiProperty({ description: 'Goals scored', example: 4 })
      for: number;

      @ApiProperty({ description: 'Goals conceded', example: 1 })
      against: number;
}

export class StandingAllStatsDto {
      @ApiProperty({ description: 'Games played', example: 2 })
      played: number;

      @ApiProperty({ description: 'Games won', example: 2 })
      win: number;

      @ApiProperty({ description: 'Games drawn', example: 0 })
      draw: number;

      @ApiProperty({ description: 'Games lost', example: 0 })
      lose: number;

      @ApiProperty({ type: StandingGoalsDto })
      goals: StandingGoalsDto;
}

export class StandingHomeAwayStatsDto {
      @ApiProperty({ description: 'Games played', example: 1 })
      played: number;

      @ApiProperty({ description: 'Games won', example: 1 })
      win: number;

      @ApiProperty({ description: 'Games drawn', example: 0 })
      draw: number;

      @ApiProperty({ description: 'Games lost', example: 0 })
      lose: number;

      @ApiProperty({ type: StandingGoalsDto })
      goals: StandingGoalsDto;
}

export class StandingItemDto {
      @ApiProperty({ description: 'Team position in standings', example: 1 })
      rank: number;

      @ApiProperty({ type: StandingTeamDto })
      team: StandingTeamDto;

      @ApiProperty({ description: 'Total points', example: 6 })
      points: number;

      @ApiProperty({ description: 'Goal difference', example: 3 })
      goalsDiff: number;

      @ApiProperty({ description: 'Group name (for tournaments)', example: 'Group A', required: false })
      group?: string;

      @ApiProperty({ description: 'Recent form', example: 'WW' })
      form: string;

      @ApiProperty({ description: 'Position status', example: 'same' })
      status: string;

      @ApiProperty({ description: 'Qualification description', example: null, required: false })
      description?: string;

      @ApiProperty({ type: StandingAllStatsDto })
      all: StandingAllStatsDto;

      @ApiProperty({ type: StandingHomeAwayStatsDto })
      home: StandingHomeAwayStatsDto;

      @ApiProperty({ type: StandingHomeAwayStatsDto })
      away: StandingHomeAwayStatsDto;

      @ApiProperty({ description: 'Last update timestamp', example: '2025-01-25T15:30:00+00:00' })
      update: string;
}

export class StandingLeagueDto {
      @ApiProperty({ description: 'League external ID', example: 850 })
      id: number;

      @ApiProperty({ description: 'League name', example: 'UEFA Euro U21 Championship' })
      name: string;

      @ApiProperty({ description: 'Country name', example: 'World' })
      country: string;

      @ApiProperty({ description: 'League logo URL', example: 'https://media.api-sports.io/football/leagues/850.png' })
      logo: string;

      @ApiProperty({ description: 'Country flag URL', example: null, required: false })
      flag?: string;

      @ApiProperty({ description: 'Season year', example: 2025 })
      season: number;

      @ApiProperty({
            description: 'Standings grouped by league groups',
            isArray: true,
            type: [StandingItemDto]
      })
      standings: StandingItemDto[][];
}

export class StandingResponseItemDto {
      @ApiProperty({ type: StandingLeagueDto })
      league: StandingLeagueDto;
}

export class ExternalStandingsResponseDto {
      @ApiProperty({
            description: 'API endpoint name',
            example: 'standings'
      })
      get: string;

      @ApiProperty({
            description: 'Query parameters used',
            example: { league: '39', season: '2024' }
      })
      parameters: Record<string, string>;

      @ApiProperty({
            description: 'API errors (if any)',
            type: [String],
            example: []
      })
      errors: string[];

      @ApiProperty({
            description: 'Number of results',
            example: 1
      })
      results: number;

      @ApiProperty({
            description: 'Pagination info',
            example: { current: 1, total: 1 }
      })
      paging: {
            current: number;
            total: number;
      };

      @ApiProperty({
            description: 'List of league standings',
            type: [StandingResponseItemDto]
      })
      response: StandingResponseItemDto[];
}
