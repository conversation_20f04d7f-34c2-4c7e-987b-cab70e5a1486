import { En<PERSON>ty, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn, Unique } from 'typeorm';

interface TeamStandingData {
      position: number;
      points: number;
      played: number;
      win: number;
      draw: number;
      lose: number;
      goals: {
            for: number;
            against: number;
      };
      goalsDiff: number;
      form: string;
      status: string;
      description: string;
      update: string;
}

interface StandingGroup {
      name: string;
      standings: TeamStandingData[];
}

@Entity('standings')
@Unique('UQ_STANDINGS_LEAGUE_SEASON_TEAM', ['leagueId', 'season', 'teamId'])
export class Standing {
      @PrimaryGeneratedColumn()
      id: number;

      @Index()
      @Column()
      leagueId: number;

      @Column()
      leagueName: string;

      @Index()
      @Column()
      season: number;

      @Index()
      @Column()
      teamId: number;

      @Column()
      teamName: string;

      @Column({ nullable: true })
      teamLogo: string;

      @Column()
      position: number;

      @Column()
      points: number;

      @Column()
      played: number;

      @Column()
      win: number;

      @Column()
      draw: number;

      @Column()
      lose: number;

      @Column()
      goalsFor: number;

      @Column()
      goalsAgainst: number;

      @Column()
      goalsDiff: number;

      @Column({ nullable: true })
      form: string;

      @Column({ nullable: true })
      status: string;

      @Column({ nullable: true })
      description: string;

      @Column({ nullable: true })
      groupName: string;

      @Column({ type: 'jsonb' })
      data: StandingGroup;

      @Column()
      source: 'api' | 'manual';

      @Column('integer', { nullable: true })
      createdBy: number | null;

      @CreateDateColumn()
      createdAt: Date;

      @UpdateDateColumn()
      updatedAt: Date;
}
