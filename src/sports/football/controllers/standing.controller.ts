import { Controller, Get, Query, ParseIntPipe, Param } from '@nestjs/common';
import {
      ApiTags,
      ApiOperation,
      ApiResponse,
      ApiParam,
      ApiQuery,
} from '@nestjs/swagger';
import { StandingService } from '../services/standing.service';
import { GetStandingsDto, StandingResponseDto, PaginatedStandingsResponse } from '../models/standing.dto';
import { ExternalStandingsResponseDto } from '../models/standing-external.dto';
import { Public } from '../../../auth/core/decorators/auth.decorators';

@ApiTags('Football - Standings')
@Controller('football/standings')
export class StandingController {
      constructor(private readonly standingService: StandingService) { }

      @ApiOperation({
            summary: 'Get League Standings (Public)',
            description: `
    Retrieve league standings with comprehensive team performance data.

    **🔓 PUBLIC ACCESS:**
    This endpoint is publicly accessible and does not require authentication.

    **Features:**
    - Complete league standings with team statistics
    - Multi-threaded image processing for team logos
    - Smart caching system (1-hour TTL)
    - Parallel database upserts for optimal performance
    - Support for leagues with groups/conferences

    **Performance Optimizations:**
    - 🚀 **Parallel Image Downloads:** Team logos downloaded concurrently
    - 🚀 **Batch Processing:** Database upserts in optimized batches of 10
    - 🚀 **Smart Caching:** Cache-first strategy with 1-hour expiration
    - 🚀 **Multi-threaded Processing:** Multiple standings processed simultaneously

    **Query Parameters:**
    - **league:** League external ID (e.g., 39 for Premier League)
    - **season:** Season year (e.g., 2024)
    - **team:** Filter by specific team
    - **format:** Response format - 'internal' (default, flat array) or 'external' (nested like api-sports.io)
    - **newdb:** Force fresh API fetch (bypass cache)

    **Examples:**
    - ?league=39&season=2024 (Premier League 2024 standings)
    - ?league=140&season=2024 (La Liga standings)
    - ?league=61&season=2024 (Ligue 1 standings)
    - ?league=78&season=2024 (Bundesliga standings)
    - ?team=33 (Manchester United position across leagues)

    **Response Data:**
    - Team position, points, games played
    - Win/Draw/Loss statistics
    - Goals for/against and goal difference
    - Recent form (last 5 matches)
    - Team logos and league information

    **Use Cases:**
    - League table displays for websites/apps
    - Team performance analysis
    - Season progress tracking
    - Mobile app league sections
    - Sports analytics dashboards
    `
      })
      @ApiQuery({
            name: 'league',
            required: false,
            type: Number,
            description: 'League external ID (e.g., 39 for Premier League)',
            example: 39
      })
      @ApiQuery({
            name: 'season',
            required: false,
            type: Number,
            description: 'Season year',
            example: 2024
      })
      @ApiQuery({
            name: 'team',
            required: false,
            type: Number,
            description: 'Team external ID to filter specific team',
            example: 33
      })
      @ApiQuery({
            name: 'page',
            required: false,
            type: Number,
            description: 'Page number for pagination',
            example: 1
      })
      @ApiQuery({
            name: 'limit',
            required: false,
            type: Number,
            description: 'Items per page (max 50)',
            example: 20
      })
      @ApiQuery({
            name: 'newdb',
            required: false,
            type: Boolean,
            description: 'Force fetch from API (bypass cache)',
            example: false
      })
      @ApiQuery({
            name: 'format',
            required: false,
            type: String,
            enum: ['internal', 'external'],
            description: 'Response format: "internal" (flat array) or "external" (nested like api-sports.io)',
            example: 'internal'
      })
      @ApiResponse({
            status: 200,
            description: 'Standings retrieved successfully - Internal Format (default)',
            example: {
                  data: [
                        {
                              id: 1,
                              leagueId: 39,
                              leagueName: 'Premier League',
                              season: 2024,
                              teamId: 33,
                              teamName: 'Manchester United',
                              teamLogo: '/images/teams/33.png',
                              position: 1,
                              points: 75,
                              played: 30,
                              win: 22,
                              draw: 9,
                              lose: 1,
                              goalsFor: 65,
                              goalsAgainst: 20,
                              goalsDiff: 45,
                              form: 'WWWDW'
                        }
                  ],
                  meta: {
                        totalItems: 20,
                        totalPages: 1,
                        currentPage: 1,
                        limit: 20
                  },
                  status: 200
            }
      })
      @ApiResponse({
            status: 200,
            description: 'Standings retrieved successfully - External Format (format=external)',
            example: {
                  get: "standings",
                  parameters: {
                        league: "39",
                        season: "2024"
                  },
                  errors: [],
                  results: 1,
                  paging: {
                        current: 1,
                        total: 1
                  },
                  response: [
                        {
                              league: {
                                    id: 39,
                                    name: "Premier League",
                                    country: "England",
                                    logo: "/images/leagues/39.png",
                                    flag: "/images/countries/gb.png",
                                    season: 2024,
                                    standings: [
                                          [
                                                {
                                                      rank: 1,
                                                      team: {
                                                            id: 33,
                                                            name: "Manchester United",
                                                            logo: "/images/teams/33.png"
                                                      },
                                                      points: 75,
                                                      goalsDiff: 45,
                                                      group: "Premier League",
                                                      form: "WWWDW",
                                                      status: "same",
                                                      description: "Promotion - Champions League (Group Stage)",
                                                      all: {
                                                            played: 30,
                                                            win: 22,
                                                            draw: 9,
                                                            lose: 1,
                                                            goals: {
                                                                  for: 65,
                                                                  against: 20
                                                            }
                                                      }
                                                }
                                          ]
                                    ]
                              }
                        }
                  ]
            }
      })
      @ApiResponse({
            status: 400,
            description: 'Invalid query parameters',
            example: {
                  message: 'Invalid league ID: must be a positive integer',
                  error: 'Bad Request',
                  statusCode: 400
            }
      })
      @Public()
      @Get()
      async getStandings(@Query() query: GetStandingsDto): Promise<PaginatedStandingsResponse | ExternalStandingsResponseDto> {
            return this.standingService.getStandings(query);
      }
}
