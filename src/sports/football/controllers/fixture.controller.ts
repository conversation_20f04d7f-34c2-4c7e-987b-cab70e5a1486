import { Controller, Get, Post, Patch, Delete, Param, Query, Body, BadRequestException, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FixtureService } from '../services/fixture.service';
import { FixtureStatisticsService } from '../services/fixture-statistics.service';
import { SeasonSyncService } from '../services/season-sync.service';
import { GetFixturesDto, FixtureResponseDto, PaginatedFixturesResponse, CreateFixtureDto, UpdateFixtureDto } from '../models/fixture.dto';
import { FixtureStatisticsDto } from '../models/fixture-statistics.dto';
import { GetScheduleDto } from '../models/schedule.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Fixture } from '../models/fixture.entity';
import { League } from '../models/league.entity';
import { CacheService } from '../../../core/cache/cache.service';
import { Between, In } from 'typeorm';
import { SystemJwtAuthGuard } from '../../../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../../../auth/system/guards/system-roles.guard';
import { TierAccessGuard } from '../../../auth/users/guards/tier-access.guard';
import { Public, AdminOnly, EditorPlus, Roles } from '../../../auth/core/decorators/auth.decorators';
import { SystemRole } from '../../../auth/core/types/auth.types';

@ApiTags('Football - Fixtures')
@Controller('football/fixtures')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard, TierAccessGuard)
export class FixtureController {
  constructor(
    private readonly fixtureService: FixtureService,
    private readonly statsService: FixtureStatisticsService,
    private readonly seasonSyncService: SeasonSyncService,
    @InjectRepository(Fixture)
    private readonly fixtureRepository: Repository<Fixture>,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    private readonly cacheService: CacheService,
  ) { }

  @ApiOperation({
    summary: 'Get Upcoming and Live Fixtures',
    description: `
    Retrieve upcoming and live football fixtures with smart filtering.

    **Features:**
    - Real-time fixture status updates
    - Smart time-based filtering (2.5-hour window)
    - Optimized performance (96% API call reduction)
    - Pagination support
    - No authentication required

    **Status Logic:**
    - UPCOMING: Fixtures starting in 5-10 minutes
    - LIVE: Fixtures currently in progress
    - Real-time updates every 10 seconds
    `
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination (default: 1)',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of fixtures per page (default: 10, max: 100)',
    example: 10
  })
  @ApiQuery({
    name: 'leagueId',
    required: false,
    type: Number,
    description: 'Filter by specific league ID',
    example: 39
  })
  @ApiResponse({
    status: 200,
    description: 'Upcoming and live fixtures retrieved successfully',
    example: {
      data: [
        {
          id: 1,
          externalId: 868847,
          date: '2025-05-24T15:00:00.000Z',
          status: 'UPCOMING',
          homeTeam: {
            id: 33,
            name: 'Manchester United',
            logo: 'https://media.api-sports.io/football/teams/33.png'
          },
          awayTeam: {
            id: 34,
            name: 'Newcastle',
            logo: 'https://media.api-sports.io/football/teams/34.png'
          },
          league: {
            id: 39,
            name: 'Premier League',
            country: 'England',
            logo: 'https://media.api-sports.io/football/leagues/39.png'
          },
          venue: {
            name: 'Old Trafford',
            city: 'Manchester'
          },
          score: {
            home: null,
            away: null
          }
        }
      ],
      meta: {
        totalItems: 25,
        totalPages: 3,
        currentPage: 1,
        limit: 10
      },
      status: 200
    }
  })
  @Public()
  @Get('upcoming-and-live')
  async getUpcomingAndLiveFixtures(@Query() query: GetFixturesDto): Promise<PaginatedFixturesResponse> {
    return this.statsService.getUpcomingAndLiveFixtures(query);
  }

  @ApiTags('Data Synchronization')
  @ApiOperation({
    summary: 'Trigger Season Fixtures Sync (Admin Only)',
    description: `
    Manually trigger synchronization of fixtures for active seasons.

    **🔒 AUTHENTICATION REQUIRED:**
    This endpoint requires System User authentication with Admin role.

    **IMPORTANT: Swagger UI Authentication Setup:**
    1. First login to get token: POST /system-auth/login
    2. Copy the "accessToken" from response
    3. Click "Authorize" button (🔓) at top of Swagger UI
    4. In "Value" field, enter: Bearer YOUR_ACCESS_TOKEN
    5. Click "Authorize" and "Close"
    6. Now all protected endpoints will include Authorization header

    **Step-by-Step Authentication:**

    **Step 1: Get Admin Token**
    \`\`\`bash
    curl -X POST http://localhost:3000/system-auth/login \\
      -H "Content-Type: application/json" \\
      -d '{"username": "admin", "password": "admin123456"}'
    \`\`\`

    **Step 2: Copy accessToken from response:**
    \`\`\`json
    {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.example_token_here"
    }
    \`\`\`

    **Step 3: Use in Swagger UI:**
    - Click "Authorize" button (🔓)
    - Enter: \`Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.example_token_here\`
    - Click "Authorize"

    **Admin Credentials for Testing:**
    - Username: admin
    - Password: admin123456
    - Role: admin (full system access)

    **Features:**
    - Syncs current and previous year fixtures (2024, 2025)
    - Only processes active leagues (18 leagues)
    - Batch processing with error isolation
    - Returns detailed sync statistics
    - Comprehensive error handling and logging

    **Technical Details:**
    - Processes ~1,250 fixtures per sync
    - Uses smart upsert with time-based filtering
    - Batch size: 50 fixtures per API call
    - Estimated duration: 30-60 seconds
    - Memory efficient with streaming processing

    **Use Cases:**
    - Initial data population for new deployments
    - Manual data refresh after API issues
    - Recovery from failed automated sync jobs
    - Testing sync functionality in development
    - Maintenance and data consistency checks

    **Expected Response Time:**
    - Small leagues: 5-15 seconds
    - Large leagues: 30-60 seconds
    - Full sync: 1-2 minutes

    **Rate Limiting:**
    - Admin users: No rate limits
    - Respects API Football rate limits (100 calls/day)

    **Example Request Headers:**
    \`\`\`
    GET /football/fixtures/sync/fixtures HTTP/1.1
    Host: localhost:3000
    Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.example_token_here
    Content-Type: application/json
    \`\`\`

    **Troubleshooting Swagger UI:**
    - If still getting 401 after authorization, refresh the page and re-authorize
    - Make sure token starts with "Bearer " (with space)
    - Check token expiration (tokens expire after 1 hour)
    - Verify you're using admin credentials (not editor/moderator)
    `
  })
  @ApiBearerAuth('bearer')
  @ApiResponse({
    status: 200,
    description: 'Season fixtures sync triggered successfully',
    example: {
      status: 'Sync triggered',
      fixturesUpserted: 1250,
      message: 'Successfully synced fixtures for 2024 and 2025 seasons',
      details: {
        seasonsProcessed: [2024, 2025],
        leaguesProcessed: 18,
        totalFixtures: 1250,
        duration: '45.2 seconds',
        timestamp: '2025-05-25T10:30:00.000Z'
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - System authentication required',
    example: {
      message: 'System authentication required',
      error: 'Unauthorized',
      statusCode: 401,
      help: {
        solution: 'You need to authenticate with a valid System User account',
        steps: [
          '1. Login: POST /system-auth/login with {"username": "admin", "password": "admin123456"}',
          '2. Copy the accessToken from response',
          '3. Add header: Authorization: Bearer YOUR_ACCESS_TOKEN',
          '4. Or use Swagger UI "Authorize" button'
        ],
        testCredentials: {
          admin: { username: 'admin', password: 'admin123456', role: 'admin' },
          editor: { username: 'editor1', password: 'editor123456', role: 'editor' },
          moderator: { username: 'moderator1', password: 'moderator123456', role: 'moderator' }
        },
        note: 'This endpoint requires Admin role - only admin credentials will work'
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    example: {
      message: 'Forbidden resource',
      error: 'Forbidden',
      statusCode: 403,
      help: {
        reason: 'Your account does not have sufficient permissions',
        required: 'Admin role',
        yourRole: 'editor', // Example: user has editor role
        solution: 'Contact system administrator to upgrade your role to admin',
        adminActions: [
          'Data synchronization operations',
          'System configuration changes',
          'User management',
          'Manual sync triggers'
        ],
        alternativeEndpoints: [
          'GET /football/fixtures/sync/status (Editor+ can view sync status)',
          'GET /football/fixtures (Public - no auth required)',
          'GET /football/fixtures/:id (Public - no auth required)'
        ]
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: 'Sync operation failed',
    example: {
      status: 'Error',
      message: 'Failed to sync season fixtures: API rate limit exceeded',
      error: {
        type: 'API_RATE_LIMIT_EXCEEDED',
        details: 'API Football rate limit reached (100 calls/day)',
        timestamp: '2025-05-25T10:30:00.000Z',
        retryAfter: '24 hours',
        troubleshooting: [
          'Check API Football subscription status',
          'Verify API key is valid and active',
          'Wait for rate limit reset (daily at 00:00 UTC)',
          'Contact API Football support if issue persists'
        ],
        commonErrors: {
          'API_RATE_LIMIT_EXCEEDED': 'Daily API call limit reached',
          'API_KEY_INVALID': 'API Football key is invalid or expired',
          'NETWORK_ERROR': 'Connection to API Football failed',
          'DATABASE_ERROR': 'Database connection or query failed',
          'TIMEOUT_ERROR': 'Sync operation timed out (>5 minutes)'
        },
        monitoring: {
          'Check logs': 'docker logs api-container | grep "sync"',
          'Check API status': 'GET /football/fixtures/sync/status',
          'Manual retry': 'Wait and retry this endpoint'
        }
      }
    }
  })
  @AdminOnly()
  @Get('sync/fixtures')
  async triggerSeasonFixturesSync(): Promise<{ status: string; fixturesUpserted?: number; message?: string }> {
    try {
      await this.seasonSyncService.syncSeasonFixtures();
      const fixturesCount = await this.fixtureRepository.count({
        where: {
          season: In([new Date().getUTCFullYear(), new Date().getUTCFullYear() - 1]),
          leagueId: In((await this.leagueRepository.find({ where: { active: true } })).map((league: League) => league.externalId)),
        },
      });
      return { status: 'Sync triggered', fixturesUpserted: fixturesCount };
    } catch (error) {
      return { status: 'Error', message: `Failed to sync season fixtures: ${error.message}` };
    }
  }

  @ApiTags('Data Synchronization')
  @ApiOperation({
    summary: 'Trigger Daily Sync (Admin Only)',
    description: `
    Manually trigger daily synchronization of all active league fixtures.

    **Features:**
    - Syncs all active leagues for current day
    - Full data refresh with smart upsert protection
    - Batch processing with error isolation
    - Returns detailed sync statistics
    - Requires admin authentication

    **Use Cases:**
    - Manual daily data refresh
    - Recovery from failed cronjobs
    - Testing sync functionality
    - Initial data population

    **Performance:**
    - Smart time-based filtering
    - Batch processing (50 fixtures per batch)
    - Error isolation per league
    - Cache invalidation after sync
    `
  })
  @ApiBearerAuth('bearer')
  @ApiResponse({
    status: 200,
    description: 'Daily sync triggered successfully',
    example: {
      status: 'Success',
      message: 'Daily sync completed successfully',
      success: true,
      stats: {
        leaguesProcessed: 18,
        fixturesUpserted: 245,
        errors: 0,
        duration: '2.3s'
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    example: {
      message: 'System authentication required',
      error: 'Unauthorized',
      statusCode: 401
    }
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    example: {
      message: 'Forbidden resource',
      error: 'Forbidden',
      statusCode: 403
    }
  })
  @ApiResponse({
    status: 500,
    description: 'Daily sync failed',
    example: {
      status: 'Error',
      message: 'Failed to trigger daily sync: API rate limit exceeded',
      success: false
    }
  })
  @AdminOnly()
  @Get('sync/daily')
  async triggerDailySync(): Promise<{ status: string; message: string; success: boolean; stats?: any }> {
    try {
      return await this.seasonSyncService.triggerDailySync();
    } catch (error) {
      return {
        status: 'Error',
        message: `Failed to trigger daily sync: ${error.message}`,
        success: false
      };
    }
  }

  @ApiTags('Data Synchronization')
  @ApiOperation({
    summary: 'Manual Trigger Live Sync (Debug)',
    description: 'Manually trigger live fixtures sync for debugging purposes.'
  })
  @ApiResponse({
    status: 200,
    description: 'Live sync triggered successfully',
    example: {
      message: 'Live sync completed',
      fixturesProcessed: 5,
      fixturesUpdated: 2
    }
  })
  @AdminOnly()
  @Get('sync/live-debug')
  async triggerLiveSyncDebug(): Promise<any> {
    try {
      // Import SyncService để test
      const { SyncService } = await import('../services/sync.service');
      // Note: Trong thực tế cần inject SyncService properly
      return {
        message: 'Debug endpoint created - need to implement SyncService injection',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @ApiTags('Data Synchronization')
  @ApiOperation({
    summary: 'Get Sync Status (Admin/Moderator/Editor)',
    description: `
    Retrieve current synchronization status and statistics.

    **🔒 AUTHENTICATION REQUIRED:**
    This endpoint requires System User authentication with Admin, Moderator, or Editor role.

    **Features:**
    - Last sync timestamp (UTC)
    - Today's fixtures count
    - Error tracking and reporting
    - Real-time status monitoring
    - Accessible by admin, moderator, and editor roles

    **Use Cases:**
    - Monitor sync health
    - Debug sync issues
    - Performance monitoring
    - System status dashboard
    - Operations team monitoring

    **Response Data:**
    - lastSync: ISO timestamp of last sync
    - fixtures: Count of today's fixtures
    - errors: Array of recent sync errors
    `
  })
  @ApiBearerAuth('bearer')
  @ApiResponse({
    status: 200,
    description: 'Sync status retrieved successfully',
    example: {
      lastSync: '2025-05-24T10:48:24.216Z',
      fixtures: 245,
      errors: []
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    example: {
      message: 'System authentication required',
      error: 'Unauthorized',
      statusCode: 401
    }
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin/Moderator/Editor access required',
    example: {
      message: 'Required roles: admin, moderator, editor',
      error: 'Forbidden',
      statusCode: 403,
      help: {
        reason: 'Your account does not have sufficient permissions',
        required: 'Admin, Moderator, or Editor role',
        yourRole: 'viewer',
        solution: 'Contact system administrator to upgrade your role',
        allowedRoles: ['admin', 'moderator', 'editor']
      }
    }
  })
  @Roles(SystemRole.ADMIN, SystemRole.MODERATOR, SystemRole.EDITOR)
  @Get('sync/status')
  async getSyncStatus(): Promise<{ lastSync: string; fixtures: number; errors: string[] }> {
    const lastSync = await this.cacheService.getCache('last_sync_time') || new Date().toISOString();
    const fixtures = await this.fixtureRepository.count({
      where: {
        date: Between(
          new Date(new Date().setUTCHours(0, 0, 0, 0)),
          new Date(new Date().setUTCHours(23, 59, 59, 999)),
        ),
      },
    });
    return { lastSync, fixtures, errors: [] };
  }

  @ApiOperation({
    summary: 'Get Team Schedule (Public)',
    description: `
    Retrieve fixtures schedule for a specific team.

    **🔓 PUBLIC ACCESS:**
    This endpoint is publicly accessible and does not require authentication.

    **Features:**
    - Complete team fixture history
    - Upcoming and past matches
    - Pagination support
    - Date range filtering
    - No authentication required

    **Use Cases:**
    - Team fixture calendar
    - Match history analysis
    - Upcoming games preview
    - Season schedule overview
    - Mobile app team pages
    - Website team statistics
    `
  })
  @ApiParam({
    name: 'teamId',
    type: 'number',
    description: 'Team external ID (positive integer)',
    example: 33
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of fixtures per page',
    example: 20
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Start date filter (YYYY-MM-DD)',
    example: '2025-01-01'
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'End date filter (YYYY-MM-DD)',
    example: '2025-12-31'
  })
  @ApiResponse({
    status: 200,
    description: 'Team schedule retrieved successfully',
    example: {
      data: [
        {
          id: 1,
          externalId: 868847,
          date: '2025-05-24T15:00:00.000Z',
          status: 'FT',
          homeTeam: {
            id: 33,
            name: 'Manchester United'
          },
          awayTeam: {
            id: 34,
            name: 'Newcastle'
          },
          score: {
            home: 2,
            away: 1
          },
          league: {
            name: 'Premier League'
          }
        }
      ],
      meta: {
        totalItems: 38,
        totalPages: 2,
        currentPage: 1,
        limit: 20
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid team ID',
    example: {
      message: 'Invalid teamId: must be a positive integer',
      error: 'Bad Request',
      statusCode: 400
    }
  })
  @Public()
  @Get('schedules/:teamId')
  async getTeamSchedule(
    @Param('teamId') teamId: string,
    @Query() query: GetScheduleDto,
  ): Promise<PaginatedFixturesResponse> {
    const id = parseInt(teamId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid teamId: must be a positive integer');
    }
    return this.fixtureService.getTeamSchedule(id, query);
  }

  @ApiOperation({
    summary: 'Get Fixture Statistics (Public)',
    description: `
    Retrieve detailed statistics for a specific fixture.

    **🔓 PUBLIC ACCESS:**
    This endpoint is publicly accessible and does not require authentication.

    **Features:**
    - Team performance statistics
    - Match statistics and metrics
    - Historical data analysis
    - No authentication required

    **Use Cases:**
    - Match analysis and insights
    - Team performance comparison
    - Statistical data for applications
    - Sports analytics and reporting
    - Mobile app match details
    - Website match statistics
    `
  })
  @ApiParam({
    name: 'externalId',
    type: 'number',
    description: 'Fixture external ID (positive integer)',
    example: 868847
  })
  @ApiResponse({
    status: 200,
    description: 'Fixture statistics retrieved successfully',
    example: {
      data: [
        {
          team: {
            id: 33,
            name: 'Manchester United'
          },
          statistics: [
            {
              type: 'Shots on Goal',
              value: 6
            },
            {
              type: 'Shots off Goal',
              value: 4
            },
            {
              type: 'Total Shots',
              value: 10
            },
            {
              type: 'Ball Possession',
              value: '65%'
            }
          ]
        }
      ],
      status: 200
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid external ID',
    example: {
      message: 'Invalid externalId: must be a positive integer',
      error: 'Bad Request',
      statusCode: 400
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Fixture not found',
    example: {
      message: 'Fixture not found',
      error: 'Not Found',
      statusCode: 404
    }
  })
  @Public()
  @Get('statistics/:externalId')
  async getFixtureStatistics(
    @Param('externalId') externalId: string,
  ): Promise<{ data: FixtureStatisticsDto[]; status: number; message?: string }> {
    const id = parseInt(externalId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid externalId: must be a positive integer');
    }
    return this.statsService.getStatistics(id);
  }

  @ApiOperation({
    summary: 'Get Fixtures with Filters (Public)',
    description: `
    Retrieve fixtures with comprehensive filtering options.

    **Access:** Public endpoint (no authentication required)

    **Query Parameters:**
    - page, limit: Pagination
    - league, season: Filter by league/season
    - team, venue: Filter by team/venue
    - date: Filter by specific date
    - status: Filter by status (NS, LIVE, FT, etc.)
    - timezone: Timezone (default: UTC)
    - from, to: Date range filtering
    - search: Search team names (home team vs away team)

    **Examples:**
    - ?league=39&season=2024 (Premier League 2024)
    - ?team=33&status=FT (Manchester United finished matches)
    - ?from=2024-01-01&to=2024-12-31 (Year 2024)
    - ?date=2024-05-24 (Specific date)
    - ?search=Manchester United (Search fixtures with Manchester United)
    - ?search=Liverpool vs Arsenal (Search specific matchup)
    `
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page', example: 10 })
  @ApiQuery({ name: 'league', required: false, type: Number, description: 'League ID (e.g., 39 for Premier League)', example: 39 })
  @ApiQuery({ name: 'season', required: false, type: Number, description: 'Season year', example: 2024 })
  @ApiQuery({ name: 'team', required: false, type: Number, description: 'Team ID', example: 33 })
  @ApiQuery({ name: 'venue', required: false, type: Number, description: 'Venue ID', example: 556 })
  @ApiQuery({ name: 'date', required: false, type: String, description: 'Date (YYYY-MM-DD)', example: '2024-05-24' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Status (NS,LIVE,FT)', example: 'LIVE,FT' })
  @ApiQuery({ name: 'timezone', required: false, type: String, description: 'Timezone', example: 'UTC' })
  @ApiQuery({ name: 'from', required: false, type: String, description: 'Start date (YYYY-MM-DD)', example: '2024-01-01' })
  @ApiQuery({ name: 'to', required: false, type: String, description: 'End date (YYYY-MM-DD)', example: '2024-12-31' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search team names (home team vs away team)', example: 'Manchester United' })
  @ApiResponse({
    status: 200,
    description: 'Fixtures retrieved successfully',
    example: {
      data: [
        {
          id: 1,
          externalId: 868847,
          date: '2025-05-24T15:00:00.000Z',
          status: 'FT',
          homeTeam: { id: 33, name: 'Manchester United' },
          awayTeam: { id: 34, name: 'Newcastle' },
          score: { home: 2, away: 1 },
          league: { name: 'Premier League' },
          isHot: true,
          thumb: '/uploads/fixtures/thumb_868847.jpg'
        }
      ],
      meta: { totalItems: 1250, totalPages: 125, currentPage: 1, limit: 10 }
    }
  })
  @Public()
  @Get()
  async getFixtures(@Query() query: GetFixturesDto): Promise<PaginatedFixturesResponse> {
    return this.fixtureService.getFixtures(query);
  }

  @ApiOperation({
    summary: 'Get Fixture by ID (Public)',
    description: `
    Retrieve detailed information for a specific fixture by external ID.

    **Access:** Public endpoint (no authentication required)

    **Parameter:**
    - externalId: Fixture external ID (positive integer)

    **Examples:**
    - /1274453 (Dreams vs Samartex)
    - /868847 (Manchester United vs Liverpool)
    - /1234567 (Any fixture external ID)
    `
  })
  @ApiParam({ name: 'externalId', type: 'number', description: 'Fixture external ID', example: 1274453 })
  @ApiResponse({
    status: 200,
    description: 'Fixture retrieved successfully',
    example: {
      data: {
        id: 11,
        externalId: 1274453,
        leagueId: 570,
        leagueName: 'Premier League',
        season: 2024,
        homeTeamName: 'Dreams',
        awayTeamName: 'Samartex',
        date: '2024-09-07T15:00:00.000Z',
        status: 'FT',
        goalsHome: 0,
        goalsAway: 0,
        isHot: false,
        thumb: '/uploads/fixtures/thumb_1274453.jpg'
      },
      status: 200
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Fixture not found'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid external ID'
  })
  @Public()
  @Get(':externalId')
  async getFixtureById(@Param('externalId') externalId: string): Promise<{ data: FixtureResponseDto; status: number }> {
    const id = parseInt(externalId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid externalId: must be a positive integer');
    }
    const fixture = await this.fixtureService.getFixtureById(id);
    return { data: fixture, status: 200 };
  }

  /**
   * Create a new fixture manually
   * @param createFixtureDto - Fixture data
   * @returns Created fixture
   */
  @EditorPlus()
  @Post()
  async createFixture(
    @Body() createFixtureDto: CreateFixtureDto,
  ): Promise<{ data: FixtureResponseDto; status: number }> {
    const fixture = await this.fixtureService.createFixture(createFixtureDto);
    return { data: fixture, status: 201 };
  }

  /**
   * Update an existing fixture
   * @param externalId - Fixture external ID
   * @param updateFixtureDto - Fixture data to update
   * @returns Updated fixture
   */
  @EditorPlus()
  @Patch(':externalId')
  async updateFixture(
    @Param('externalId') externalId: string,
    @Body() updateFixtureDto: UpdateFixtureDto,
  ): Promise<{ data: FixtureResponseDto; status: number }> {
    const id = parseInt(externalId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid externalId: must be a positive integer');
    }
    const fixture = await this.fixtureService.updateFixture(id, updateFixtureDto);
    return { data: fixture, status: 200 };
  }

  @ApiOperation({
    summary: 'Delete Fixture (Admin Only)',
    description: `
    Delete a fixture from the database with comprehensive validation and cache cleanup.

    **🔒 AUTHENTICATION REQUIRED:**
    This endpoint requires System User authentication with Admin role only.

    **IMPORTANT: Swagger UI Authentication Setup:**
    1. First login to get token: POST /system-auth/login
    2. Copy the "accessToken" from response
    3. Click "Authorize" button (🔓) at top of Swagger UI
    4. In "Value" field, enter: Bearer YOUR_ACCESS_TOKEN
    5. Click "Authorize" and "Close"
    6. Now all protected endpoints will include Authorization header

    **Admin Credentials for Testing:**
    - Username: admin
    - Password: admin123456
    - Role: admin (required for deletion)

    **Features:**
    - Validates fixture existence before deletion
    - Comprehensive cache cleanup (fixture lists and individual fixture cache)
    - Audit logging for deletion operations
    - Hard delete from database
    - Error handling with detailed messages

    **Security:**
    - Admin role required (highest permission level)
    - Fixture existence validation
    - Comprehensive audit trail
    - Database transaction safety

    **Cache Management:**
    - Clears fixture list cache for the league
    - Clears individual fixture cache
    - Ensures data consistency after deletion

    **Use Cases:**
    - Remove duplicate fixtures
    - Clean up test data
    - Remove cancelled/invalid fixtures
    - Database maintenance operations
    `
  })
  @ApiParam({
    name: 'externalId',
    type: 'number',
    description: 'Fixture external ID to delete',
    example: 1274453
  })
  @ApiResponse({
    status: 204,
    description: 'Fixture deleted successfully'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid external ID',
    example: {
      message: 'Invalid externalId: must be a positive integer',
      error: 'Bad Request',
      statusCode: 400
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    example: {
      message: 'Unauthorized',
      statusCode: 401
    }
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    example: {
      message: 'Forbidden resource',
      error: 'Forbidden',
      statusCode: 403,
      help: {
        reason: 'Your account does not have sufficient permissions',
        required: 'Admin role',
        yourRole: 'editor',
        solution: 'Contact system administrator to upgrade your role to admin',
        adminActions: [
          'Delete fixtures',
          'Data synchronization operations',
          'System configuration changes',
          'User management'
        ]
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Fixture not found',
    example: {
      message: 'Fixture with externalId 1274453 not found',
      error: 'Not Found',
      statusCode: 404
    }
  })
  @ApiBearerAuth('bearer')
  @AdminOnly()
  @Delete(':externalId')
  async deleteFixture(
    @Param('externalId') externalId: string,
  ): Promise<{ status: number }> {
    const id = parseInt(externalId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid externalId: must be a positive integer');
    }
    await this.fixtureService.deleteFixture(id);
    return { status: 204 };
  }
}