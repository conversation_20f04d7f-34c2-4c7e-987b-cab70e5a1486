import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { Standing } from '../models/standing.entity';
import { GetStandingsDto, StandingResponseDto, PaginatedStandingsResponse } from '../models/standing.dto';
import { ExternalStandingsResponseDto } from '../models/standing-external.dto';
import { CacheService } from '../../../core/cache/cache.service';
import { ImageService } from '../../../shared/services/image.service';

@Injectable()
export class StandingService {
      private readonly logger = new Logger(StandingService.name);

      constructor(
            @InjectRepository(Standing)
            private readonly standingRepository: Repository<Standing>,
            private readonly configService: ConfigService,
            private readonly cacheService: CacheService,
            private readonly imageService: ImageService,
      ) { }

      /**
       * Get standings by query parameters with optimized multi-threaded processing
       * @param query - Query parameters (league, season, team, page, limit, newdb, format)
       * @returns Paginated list of standings in requested format
       */
      async getStandings(query: GetStandingsDto): Promise<PaginatedStandingsResponse | ExternalStandingsResponseDto> {
            const page = query.page || 1;
            const limit = query.limit || 20;
            const format = query.format || 'internal';
            const cacheKey = `standings_list_${query.league ?? ''}_${query.season ?? ''}_${query.team ?? ''}_${page}_${limit}_${format}`;

            // Skip cache if newdb=true
            if (!query.newdb) {
                  const cached = await this.cacheService.getCache(cacheKey);
                  if (cached) {
                        this.logger.debug(`Returning standings from cache for key: ${cacheKey}`);
                        return JSON.parse(cached);
                  }
            }

            // Try database first
            let { standings, totalItems } = await this.fetchFromDb(query);

            // If no data in DB or newdb=true, fetch from API
            if (standings.length === 0 || query.newdb) {
                  this.logger.debug(`Fetching standings from API for query: ${JSON.stringify(query)}`);
                  standings = await this.fetchFromApiOptimized(query);

                  if (standings.length > 0) {
                        try {
                              // Multi-threaded upsert for performance
                              await this.upsertStandingsBatch(standings);
                              this.logger.debug(`Upserted ${standings.length} standings to DB`);

                              // Refresh from DB to get complete data
                              const paginatedResult = await this.fetchFromDb(query);
                              standings = paginatedResult.standings;
                              totalItems = paginatedResult.totalItems;
                        } catch (error) {
                              this.logger.error(`Failed to save standings to DB: ${error.message}`);
                              throw error;
                        }
                  }

                  // Clear related cache after API fetch
                  await this.cacheService.deleteByPattern(`standings_list_${query.league ?? ''}_${query.season ?? ''}_*`);
            }

            let response: PaginatedStandingsResponse | ExternalStandingsResponseDto;

            if (format === 'external') {
                  response = this.mapToExternalFormat(standings, query);
            } else {
                  response = {
                        data: this.mapToResponseDto(standings),
                        meta: {
                              totalItems,
                              totalPages: Math.ceil(totalItems / limit),
                              currentPage: page,
                              limit,
                        },
                        status: 200,
                  };
            }

            // Cache response if not newdb
            if ((format === 'internal' && (response as PaginatedStandingsResponse).data.length > 0 && !query.newdb) ||
                  (format === 'external' && (response as ExternalStandingsResponseDto).response.length > 0 && !query.newdb)) {
                  await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600); // 1 hour cache
                  this.logger.debug(`Cached standings response for key: ${cacheKey}`);
            }

            return response;
      }

      /**
       * Optimized API fetch with parallel image processing
       */
      private async fetchFromApiOptimized(query: GetStandingsDto): Promise<Standing[]> {
            try {
                  const apiQuery: any = {};
                  if (query.league) apiQuery.league = query.league;
                  if (query.season) apiQuery.season = query.season;
                  if (query.team) apiQuery.team = query.team;

                  const response = await this.executeWithRetry(async () => {
                        const apiUrl = `${this.configService.get('app.apiFootballUrl')}/standings`;
                        const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                        this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(apiQuery)}`);
                        return axios.get(apiUrl, { params: apiQuery, headers });
                  });

                  if (!response.data.response || response.data.response.length === 0) {
                        this.logger.warn(`No standings data returned from API for query ${JSON.stringify(apiQuery)}`);
                        return [];
                  }

                  // Process standings with parallel image download
                  const allStandings: Standing[] = [];

                  for (const apiData of response.data.response) {
                        if (!apiData.league || !apiData.league.standings) continue;

                        // Process each standing group (some leagues have groups)
                        for (const standingGroup of apiData.league.standings) {
                              const standingPromises = standingGroup.map(async (teamData: any) => {
                                    const standing = new Standing();

                                    // Basic fields
                                    standing.leagueId = apiData.league.id;
                                    standing.leagueName = apiData.league.name || 'Unknown';
                                    standing.season = apiData.league.season || 0;
                                    standing.teamId = teamData.team.id;
                                    standing.teamName = teamData.team.name || 'Unknown';
                                    standing.position = teamData.rank || 0;
                                    standing.points = teamData.points || 0;
                                    standing.played = teamData.all?.played || 0;
                                    standing.win = teamData.all?.win || 0;
                                    standing.draw = teamData.all?.draw || 0;
                                    standing.lose = teamData.all?.lose || 0;
                                    standing.goalsFor = teamData.all?.goals?.for || 0;
                                    standing.goalsAgainst = teamData.all?.goals?.against || 0;
                                    standing.goalsDiff = teamData.goalsDiff || 0;
                                    standing.form = teamData.form || '';
                                    standing.status = teamData.status || '';
                                    standing.description = teamData.description || '';
                                    standing.source = 'api';
                                    standing.createdBy = null;

                                    // Download team logo in parallel (non-blocking)
                                    standing.teamLogo = teamData.team.logo
                                          ? await this.downloadTeamLogo(teamData.team.logo, teamData.team.id)
                                          : '';

                                    standing.data = {
                                          name: 'Regular Season',
                                          standings: [{
                                                position: teamData.rank,
                                                points: teamData.points,
                                                played: teamData.all?.played || 0,
                                                win: teamData.all?.win || 0,
                                                draw: teamData.all?.draw || 0,
                                                lose: teamData.all?.lose || 0,
                                                goals: {
                                                      for: teamData.all?.goals?.for || 0,
                                                      against: teamData.all?.goals?.against || 0,
                                                },
                                                goalsDiff: teamData.goalsDiff || 0,
                                                form: teamData.form || '',
                                                status: teamData.status || '',
                                                description: teamData.description || '',
                                                update: teamData.update || new Date().toISOString(),
                                          }]
                                    };

                                    return standing;
                              });

                              // Execute all standings for this group in parallel
                              const groupStandings = await Promise.all(standingPromises);
                              allStandings.push(...groupStandings);
                        }
                  }

                  this.logger.debug(`Processed ${allStandings.length} standings with parallel image downloads`);
                  return allStandings;

            } catch (error) {
                  this.logger.error(`Failed to fetch standings from API: ${error.message}`);
                  return [];
            }
      }

      /**
       * Download team logo with fallback
       */
      private async downloadTeamLogo(logoUrl: string, teamId: number): Promise<string> {
            try {
                  return await this.imageService.downloadImage(logoUrl, 'teams', `${teamId}.png`);
            } catch (error) {
                  this.logger.warn(`Failed to download team logo for team ${teamId}: ${error.message}`);
                  return logoUrl; // Return original URL as fallback
            }
      }

      /**
       * Multi-threaded batch upsert for performance
       */
      private async upsertStandingsBatch(standings: Standing[]): Promise<void> {
            const batchSize = 10; // Process 10 standings per batch
            const batches = this.chunkArray(standings, batchSize);

            const batchPromises = batches.map(async (batch, index) => {
                  try {
                        await this.standingRepository.upsert(batch, ['leagueId', 'season', 'teamId']);
                        this.logger.debug(`Batch ${index + 1}/${batches.length} upserted successfully`);
                  } catch (error) {
                        this.logger.error(`Failed to upsert batch ${index + 1}: ${error.message}`);
                        throw error;
                  }
            });

            // Execute all batches in parallel
            await Promise.all(batchPromises);
            this.logger.debug(`All ${batches.length} batches upserted successfully`);
      }

      /**
       * Utility: Split array into chunks
       */
      private chunkArray<T>(array: T[], size: number): T[][] {
            const chunks: T[][] = [];
            for (let i = 0; i < array.length; i += size) {
                  chunks.push(array.slice(i, i + size));
            }
            return chunks;
      }

      /**
       * Fetch standings from database
       */
      private async fetchFromDb(query: GetStandingsDto): Promise<{ standings: Standing[]; totalItems: number }> {
            const page = query.page || 1;
            const limit = query.limit || 20;
            const skip = (page - 1) * limit;

            const qb = this.standingRepository.createQueryBuilder('standing');

            if (query.league) {
                  qb.andWhere('standing.leagueId = :leagueId', { leagueId: query.league });
            }
            if (query.season) {
                  qb.andWhere('standing.season = :season', { season: query.season });
            }
            if (query.team) {
                  qb.andWhere('standing.teamId = :teamId', { teamId: query.team });
            }

            qb.orderBy('standing.position', 'ASC');

            const [standings, totalItems] = await qb
                  .skip(skip)
                  .take(limit)
                  .getManyAndCount();

            this.logger.debug(`Fetched ${standings.length} standings from DB for query: ${JSON.stringify(query)}`);
            return { standings, totalItems };
      }

      /**
       * Execute API call with retry logic
       */
      private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
            for (let attempt = 1; attempt <= retries; attempt++) {
                  try {
                        return await fn();
                  } catch (error) {
                        if (attempt === retries) {
                              this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                              throw error;
                        }
                        this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                        await new Promise((resolve) => setTimeout(resolve, delay));
                  }
            }
            throw new Error('Unexpected error in retry logic');
      }

      /**
       * Map Standing entities to response DTOs
       */
      private mapToResponseDto(standings: Standing[]): StandingResponseDto[] {
            return standings.map(standing => ({
                  id: standing.id,
                  leagueId: standing.leagueId,
                  leagueName: standing.leagueName,
                  season: standing.season,
                  teamId: standing.teamId,
                  teamName: standing.teamName,
                  teamLogo: standing.teamLogo,
                  position: standing.position,
                  points: standing.points,
                  played: standing.played,
                  win: standing.win,
                  draw: standing.draw,
                  lose: standing.lose,
                  goalsFor: standing.goalsFor,
                  goalsAgainst: standing.goalsAgainst,
                  goalsDiff: standing.goalsDiff,
                  form: standing.form,
                  groupName: standing.groupName,
            }));
      }

      /**
       * Map Standing entities to external API format (api-sports.io compatible)
       */
      private mapToExternalFormat(standings: Standing[], query: GetStandingsDto): ExternalStandingsResponseDto {
            // Group standings by league (since external API groups by league)
            const leagueGroups = standings.reduce((groups, standing) => {
                  const key = `${standing.leagueId}-${standing.season}`;
                  if (!groups[key]) {
                        groups[key] = {
                              leagueId: standing.leagueId,
                              leagueName: standing.leagueName,
                              season: standing.season,
                              standings: []
                        };
                  }
                  groups[key].standings.push(standing);
                  return groups;
            }, {} as Record<string, { leagueId: number; leagueName: string; season: number; standings: Standing[] }>);

            const response = Object.values(leagueGroups).map(group => ({
                  league: {
                        id: group.leagueId,
                        name: group.leagueName,
                        country: 'Unknown', // TODO: Add country info to Standing entity if needed
                        logo: '',
                        flag: '',
                        season: group.season,
                        standings: [
                              group.standings.map(standing => ({
                                    rank: standing.position,
                                    team: {
                                          id: standing.teamId,
                                          name: standing.teamName,
                                          logo: standing.teamLogo || ''
                                    },
                                    points: standing.points,
                                    goalsDiff: standing.goalsDiff,
                                    group: standing.groupName || 'League',
                                    form: standing.form || '',
                                    status: '',
                                    description: '',
                                    all: {
                                          played: standing.played,
                                          win: standing.win,
                                          draw: standing.draw,
                                          lose: standing.lose,
                                          goals: {
                                                for: standing.goalsFor,
                                                against: standing.goalsAgainst
                                          }
                                    },
                                    home: {
                                          played: Math.floor(standing.played / 2), // Approximation since we don't store home/away separately
                                          win: Math.floor(standing.win / 2),
                                          draw: Math.floor(standing.draw / 2),
                                          lose: Math.floor(standing.lose / 2),
                                          goals: {
                                                for: Math.floor(standing.goalsFor / 2),
                                                against: Math.floor(standing.goalsAgainst / 2)
                                          }
                                    },
                                    away: {
                                          played: Math.ceil(standing.played / 2),
                                          win: Math.ceil(standing.win / 2),
                                          draw: Math.ceil(standing.draw / 2),
                                          lose: Math.ceil(standing.lose / 2),
                                          goals: {
                                                for: Math.ceil(standing.goalsFor / 2),
                                                against: Math.ceil(standing.goalsAgainst / 2)
                                          }
                                    },
                                    update: new Date().toISOString()
                              }))
                        ]
                  }
            }));

            return {
                  get: 'standings',
                  parameters: Object.fromEntries(
                        Object.entries({
                              league: query.league?.toString(),
                              season: query.season?.toString(),
                              team: query.team?.toString()
                        }).filter(([_, value]) => value !== undefined)
                  ) as Record<string, string>,
                  errors: [],
                  results: response.length,
                  paging: {
                        current: query.page || 1,
                        total: 1 // External API doesn't paginate standings
                  },
                  response
            };
      }
}
