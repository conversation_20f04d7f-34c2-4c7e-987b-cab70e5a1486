import { ConsoleLogger, Injectable } from '@nestjs/common';

@Injectable()
export class CustomLoggerService extends ConsoleLogger {
      log(message: any, context?: string) {
            if (process.env.NODE_ENV !== 'production') {
                  super.log(message, context);
            }
      }

      error(message: any, trace?: string, context?: string) {
            if (process.env.NODE_ENV !== 'production') {
                  super.error(message, trace, context);
            }
      }

      warn(message: any, context?: string) {
            if (process.env.NODE_ENV !== 'production') {
                  super.warn(message, context);
            }
      }

      debug(message: any, context?: string) {
            if (process.env.NODE_ENV !== 'production') {
                  super.debug(message, context);
            }
      }

      verbose(message: any, context?: string) {
            if (process.env.NODE_ENV !== 'production') {
                  super.verbose(message, context);
            }
      }
}
