{"name": "api-sports-game", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:api": "nest build --webpack", "build:worker": "tsc --project tsconfig.worker.json", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_ENV=production TZ=UTC nest start", "start:silent": "NODE_ENV=production TZ=UTC nest start > /dev/null 2>&1", "start:dev": "TZ=UTC nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:api": "TZ=UTC nest start", "start:api:dev": "TZ=UTC nest start --watch", "start:api:prod": "node dist/main", "start:worker": "TZ=UTC node dist/src/worker.js", "start:worker:dev": "TZ=UTC nodemon --watch dist/src/worker.js dist/src/worker.js", "start:worker:prod": "node dist/src/worker.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:start:dev": "pm2 start ecosystem.dev.config.js", "pm2:stop": "pm2 stop all", "pm2:restart": "pm2 restart all", "pm2:delete": "pm2 delete all", "pm2:logs": "pm2 logs --lines 20", "pm2:logs:live": "pm2 logs", "pm2:status": "pm2 status", "pm2:flush": "pm2 flush", "instances:start": "./scripts/manage-instances.sh start", "instances:stop": "./scripts/manage-instances.sh stop", "instances:restart": "./scripts/manage-instances.sh restart", "instances:status": "./scripts/manage-instances.sh status", "instances:ports": "./scripts/manage-instances.sh ports", "instances:test": "./scripts/manage-instances.sh test", "instances:monitor": "./scripts/manage-instances.sh monitor", "api-3000:start": "./scripts/manage-instances.sh start api-3000", "api-3000:stop": "./scripts/manage-instances.sh stop api-3000", "api-3000:restart": "./scripts/manage-instances.sh restart api-3000", "api-3000:logs": "./scripts/manage-instances.sh logs api-3000", "api-3001:start": "./scripts/manage-instances.sh start api-3001", "api-3001:stop": "./scripts/manage-instances.sh stop api-3001", "api-3001:restart": "./scripts/manage-instances.sh restart api-3001", "api-3001:logs": "./scripts/manage-instances.sh logs api-3001", "worker:start": "./scripts/manage-instances.sh start worker", "worker:stop": "./scripts/manage-instances.sh stop worker", "worker:restart": "./scripts/manage-instances.sh restart worker", "worker:logs": "./scripts/manage-instances.sh logs worker", "logs:clean": "./scripts/manage-logs.sh clean", "logs:view": "./scripts/manage-logs.sh view-api", "logs:monitor": "./scripts/manage-logs.sh monitor", "logs:errors": "./scripts/manage-logs.sh view-errors", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --config jest.config.js --runInBand", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/bull": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.1", "@nestjs/platform-socket.io": "^11.1.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.1", "@socket.io/redis-adapter": "^8.3.0", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "handlebars": "^4.7.8", "ioredis": "^5.6.1", "multer": "^2.0.0", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.24"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.14", "@types/node": "^22.15.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}