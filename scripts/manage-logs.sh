#!/bin/bash

# APISportsGame Log Management Script
# Usage: ./scripts/manage-logs.sh [command]

case "$1" in
    "clean")
        echo "🧹 Cleaning old PM2 logs..."
        pm2 flush
        rm -f logs/*.log
        echo "✅ Logs cleaned successfully"
        ;;
    "rotate")
        echo "🔄 Rotating PM2 logs..."
        pm2 reloadLogs
        echo "✅ Logs rotated successfully"
        ;;
    "view-api")
        echo "📖 Viewing API logs (last 50 lines)..."
        tail -f logs/api-out.log
        ;;
    "view-worker")
        echo "📖 Viewing Worker logs (last 50 lines)..."
        tail -f logs/worker-out.log
        ;;
    "view-errors")
        echo "🚨 Viewing Error logs..."
        echo "=== API Errors ==="
        tail -20 logs/api-error.log 2>/dev/null || echo "No API errors"
        echo "=== Worker Errors ==="
        tail -20 logs/worker-error.log 2>/dev/null || echo "No Worker errors"
        ;;
    "size")
        echo "📊 Log file sizes:"
        du -h logs/*.log 2>/dev/null || echo "No log files found"
        ;;
    "monitor")
        echo "📊 Real-time log monitoring..."
        echo "Press Ctrl+C to stop"
        tail -f logs/api-out.log logs/worker-out.log
        ;;
    *)
        echo "🔧 APISportsGame Log Management"
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  clean      - Clean all logs"
        echo "  rotate     - Rotate logs"
        echo "  view-api   - View API logs"
        echo "  view-worker - View Worker logs"
        echo "  view-errors - View error logs"
        echo "  size       - Show log file sizes"
        echo "  monitor    - Monitor logs in real-time"
        ;;
esac
