#!/bin/bash

# =====================================================
# SCRIPT TẠO DEMO NEWS & CATEGORIES
# =====================================================
# Script này tạo 5 categories và 20 bài news mẫu cho demo
# Sử dụng: bash scripts/create-demo-news.sh

echo "🚀 Bắt đầu tạo demo news và categories..."

# Kiểm tra database connection
echo "📡 Kiểm tra kết nối database..."
if ! sudo -u postgres psql -d testlivesport -c "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ Không thể kết nối database 'testlivesport'"
    echo "💡 Hãy kiểm tra:"
    echo "   - Database đã được tạo chưa?"
    echo "   - PostgreSQL service đang chạy?"
    echo "   - Tên database trong .env có đúng không?"
    exit 1
fi

echo "✅ Database connection OK"

# Chạy SQL script
echo "📝 Đang import sample data..."
if sudo -u postgres psql -d testlivesport -f scripts/vnexpress-sample-data.sql; then
    echo "✅ Import thành công!"
else
    echo "❌ Import thất bại!"
    exit 1
fi

# Kiểm tra kết quả
echo "🔍 Kiểm tra kết quả..."

# Đếm categories
CATEGORY_COUNT=$(sudo -u postgres psql -d testlivesport -t -c "SELECT COUNT(*) FROM news_categories WHERE slug IN ('bong-da-viet-nam', 'bong-da-quoc-te', 'chuyen-nhuong', 'ket-qua-tran-dau', 'phong-su-bong-da');" | xargs)

# Đếm articles
ARTICLE_COUNT=$(sudo -u postgres psql -d testlivesport -t -c "SELECT COUNT(*) FROM news_articles WHERE \"categoryId\" IN (SELECT id FROM news_categories WHERE slug IN ('bong-da-viet-nam', 'bong-da-quoc-te', 'chuyen-nhuong', 'ket-qua-tran-dau', 'phong-su-bong-da'));" | xargs)

echo "📊 Kết quả:"
echo "   - Categories: $CATEGORY_COUNT/5"
echo "   - Articles: $ARTICLE_COUNT/20"

if [ "$CATEGORY_COUNT" -eq 5 ] && [ "$ARTICLE_COUNT" -eq 20 ]; then
    echo "🎉 THÀNH CÔNG! Demo data đã được tạo hoàn chỉnh"
    echo ""
    echo "🔗 Test endpoints:"
    echo "   Categories: curl 'http://localhost:3000/news/categories'"
    echo "   Article: curl 'http://localhost:3000/news/doi-tuyen-viet-nam-chuan-bi-vong-loai-world-cup-2026'"
    echo "   Admin: curl 'http://localhost:3000/admin/news/articles?limit=5' -H 'Authorization: Bearer [TOKEN]'"
    echo ""
    echo "📱 Swagger UI: http://localhost:3000/api"
else
    echo "⚠️  Có vấn đề với việc import data"
    echo "💡 Hãy kiểm tra logs ở trên để debug"
fi

echo "✨ Hoàn thành!"
