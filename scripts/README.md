# 🔧 News System Update Scripts

This directory contains example scripts and documentation for updating News Articles and Categories in the APISportsGame News System.

## 📁 **Files Overview**

- **`news-update-examples.js`** - Node.js/JavaScript examples
- **`news_update_examples.py`** - Python examples  
- **`README.md`** - This documentation file

## 🚀 **Quick Start**

### **Prerequisites**

1. **APISportsGame server running** on `http://localhost:3000`
2. **Admin credentials**: username: `admin`, password: `admin123456`
3. **Dependencies installed** (see below)

### **Option 1: Node.js/JavaScript**

```bash
# Install dependencies
npm install axios

# Run examples
node scripts/news-update-examples.js
```

### **Option 2: Python**

```bash
# Install dependencies
pip install requests

# Run examples
python scripts/news_update_examples.py
```

### **Option 3: cURL (Bash)**

```bash
# See LogWorking/58_NEWS_SYSTEM_UPDATE_SCRIPTS.md for cURL examples
```

---

## 📰 **Update Operations Supported**

### **News Articles:**
- ✅ Update title, content, excerpt
- ✅ Change status (draft/published/archived)
- ✅ Set featured flag and priority
- ✅ Update tags and metadata
- ✅ Change category assignment
- ✅ Set football-related links (league, team, player, fixture)
- ✅ Update SEO fields (metaTitle, metaDescription)
- ✅ Set custom publishedAt date

### **News Categories:**
- ✅ Update name and description
- ✅ Change display order (sortOrder)
- ✅ Update UI styling (icon, color)
- ✅ Toggle active/public status
- ✅ Update SEO fields
- ✅ Bulk reorder operations

### **Special Operations:**
- ✅ Publish/unpublish articles
- ✅ Archive articles
- ✅ Reorder multiple categories
- ✅ Toggle category status

---

## 🔑 **Authentication**

All update operations require System Authentication:

1. **Login** with admin credentials
2. **Get JWT token** from response
3. **Include token** in Authorization header: `Bearer YOUR_TOKEN`

```javascript
// Example login
const response = await axios.post('http://localhost:3000/system-auth/login', {
    username: 'admin',
    password: 'admin123456'
});
const token = response.data.access_token;
```

---

## 📋 **Example Usage**

### **JavaScript Example:**

```javascript
const { NewsUpdateClient } = require('./news-update-examples.js');

async function updateExample() {
    const client = new NewsUpdateClient();
    await client.login();
    
    // Update article
    const updatedArticle = await client.updateArticle(1, {
        title: 'New Title',
        priority: 8,
        isFeatured: true
    });
    
    // Update category
    const updatedCategory = await client.updateCategory(1, {
        name: 'Updated Category Name',
        color: '#FF6B35'
    });
}
```

### **Python Example:**

```python
from news_update_examples import NewsUpdateClient

def update_example():
    client = NewsUpdateClient()
    client.login()
    
    # Update article
    updated_article = client.update_article(1, {
        'title': 'New Title',
        'priority': 8,
        'isFeatured': True
    })
    
    # Update category
    updated_category = client.update_category(1, {
        'name': 'Updated Category Name',
        'color': '#FF6B35'
    })
```

### **cURL Example:**

```bash
# Login first
TOKEN=$(curl -s -X POST http://localhost:3000/system-auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123456"}' \
  | jq -r '.access_token')

# Update article
curl -X PATCH http://localhost:3000/admin/news/articles/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "New Title",
    "priority": 8,
    "isFeatured": true
  }'
```

---

## 🎯 **Common Update Scenarios**

### **1. Publish Draft Article:**

```javascript
// Method 1: Update status
await client.updateArticle(articleId, { status: 'published' });

// Method 2: Use publish endpoint
await client.publishArticle(articleId);
```

### **2. Feature Article:**

```javascript
await client.updateArticle(articleId, {
    isFeatured: true,
    priority: 10
});
```

### **3. Change Article Category:**

```javascript
await client.updateArticle(articleId, {
    categoryId: 2,
    tags: ['new-category', 'updated']
});
```

### **4. Update SEO Fields:**

```javascript
await client.updateArticle(articleId, {
    metaTitle: 'SEO Optimized Title',
    metaDescription: 'SEO optimized description for better search ranking'
});
```

### **5. Reorder Categories:**

```javascript
await client.reorderCategories([
    { id: 1, sortOrder: 3 },
    { id: 2, sortOrder: 1 },
    { id: 3, sortOrder: 2 }
]);
```

---

## ⚠️ **Important Notes**

### **Field Restrictions:**
- **`slug`** cannot be updated (URL stability)
- **`id`** cannot be changed
- **`authorId`** cannot be updated
- **`viewCount`, `shareCount`, `likeCount`** have separate increment endpoints

### **Validation Rules:**
- **Article title**: 5-500 characters
- **Article content**: 10-50,000 characters
- **Category name**: 2-200 characters
- **Priority**: 0-100
- **Color**: Valid hex format (#RRGGBB)

### **Business Logic:**
- **publishedAt** is auto-set when status changes to 'published'
- **Category counts** are auto-updated when articles change category
- **Cache** is automatically invalidated on updates

---

## 🚨 **Error Handling**

### **Common Errors:**

1. **401 Unauthorized** - Token expired or invalid
   ```bash
   # Solution: Login again to get new token
   ```

2. **404 Not Found** - Article/Category doesn't exist
   ```bash
   # Solution: Check if ID exists
   curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:3000/admin/news/articles/1
   ```

3. **400 Bad Request** - Validation error
   ```bash
   # Solution: Check field requirements and formats
   ```

4. **409 Conflict** - Slug already exists (for create operations)

---

## 📊 **Response Format**

### **Successful Update Response:**

```json
{
  "id": 1,
  "title": "Updated Article Title",
  "slug": "original-slug-unchanged",
  "status": "published",
  "publishedAt": "2025-06-02T10:00:00.000Z",
  "category": {
    "id": 1,
    "name": "Category Name",
    "slug": "category-slug"
  },
  "updatedAt": "2025-06-02T10:30:00.000Z"
}
```

### **Error Response:**

```json
{
  "message": "Article with ID 999 not found",
  "error": "Not Found", 
  "statusCode": 404
}
```

---

## 🔗 **Related Documentation**

- **Complete API Reference**: `LogWorking/58_NEWS_SYSTEM_UPDATE_SCRIPTS.md`
- **Updateable Fields Guide**: `LogWorking/57_NEWS_SYSTEM_UPDATEABLE_FIELDS_COMPLETE.md`
- **News System Overview**: `LogWorking/54_NEWS_SYSTEM_TEST_CASES_IMPLEMENTATION_COMPLETE.md`

---

## 🆘 **Support**

If you encounter issues:

1. **Check server status**: `pm2 status`
2. **Check logs**: `pm2 logs`
3. **Verify credentials**: Test login endpoint
4. **Check database**: Ensure articles/categories exist
5. **Review validation**: Check field requirements

**Happy updating! 🚀**
