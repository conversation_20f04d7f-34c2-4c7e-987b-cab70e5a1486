#!/usr/bin/env python3

"""
News System Update Examples - Python Version

This script demonstrates how to update News Articles and Categories
using the APISportsGame News System API.

Usage:
    python scripts/news_update_examples.py

Requirements:
    pip install requests
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Optional

# Configuration
API_BASE_URL = 'http://localhost:3000'
ADMIN_CREDENTIALS = {
    'username': 'admin',
    'password': 'admin123456'
}

class NewsUpdateClient:
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.token: Optional[str] = None
        self.session = requests.Session()

    def login(self) -> str:
        """Login and get JWT token"""
        try:
            print('🔐 Logging in...')
            response = self.session.post(
                f'{self.base_url}/system-auth/login',
                json=ADMIN_CREDENTIALS
            )
            response.raise_for_status()
            
            data = response.json()
            self.token = data['access_token']
            
            # Set default authorization header
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}',
                'Content-Type': 'application/json'
            })
            
            print('✅ Login successful!')
            print(f"👤 User: {data['user']['username']} ({data['user']['role']})")
            return self.token
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Login failed: {e}')
            raise

    def update_article(self, article_id: int, update_data: Dict) -> Dict:
        """Update News Article"""
        try:
            print(f'📰 Updating article ID: {article_id}')
            response = self.session.patch(
                f'{self.base_url}/admin/news/articles/{article_id}',
                json=update_data
            )
            response.raise_for_status()
            
            print('✅ Article updated successfully!')
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Article update failed: {e}')
            if hasattr(e, 'response') and e.response is not None:
                print(f'Response: {e.response.text}')
            raise

    def update_category(self, category_id: int, update_data: Dict) -> Dict:
        """Update News Category"""
        try:
            print(f'🗂️ Updating category ID: {category_id}')
            response = self.session.patch(
                f'{self.base_url}/admin/news/categories/{category_id}',
                json=update_data
            )
            response.raise_for_status()
            
            print('✅ Category updated successfully!')
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Category update failed: {e}')
            if hasattr(e, 'response') and e.response is not None:
                print(f'Response: {e.response.text}')
            raise

    def publish_article(self, article_id: int) -> Dict:
        """Publish Article"""
        try:
            print(f'📢 Publishing article ID: {article_id}')
            response = self.session.post(
                f'{self.base_url}/admin/news/articles/{article_id}/publish'
            )
            response.raise_for_status()
            
            print('✅ Article published successfully!')
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Article publish failed: {e}')
            raise

    def reorder_categories(self, orders: List[Dict]) -> Dict:
        """Reorder Categories"""
        try:
            print('🔄 Reordering categories...')
            response = self.session.post(
                f'{self.base_url}/admin/news/categories/reorder',
                json=orders
            )
            response.raise_for_status()
            
            print('✅ Categories reordered successfully!')
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Category reorder failed: {e}')
            raise

    def get_articles(self, params: Dict = None) -> Dict:
        """Get Articles List"""
        try:
            response = self.session.get(
                f'{self.base_url}/admin/news/articles',
                params=params or {}
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Get articles failed: {e}')
            raise

    def get_categories(self, params: Dict = None) -> Dict:
        """Get Categories List"""
        try:
            response = self.session.get(
                f'{self.base_url}/admin/news/categories',
                params=params or {}
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f'❌ Get categories failed: {e}')
            raise

def run_examples():
    """Run example update operations"""
    client = NewsUpdateClient()
    
    try:
        # 1. Login
        client.login()
        
        print('\n' + '=' * 50)
        print('📋 GETTING CURRENT DATA')
        print('=' * 50)
        
        # 2. Get current data
        articles = client.get_articles({'limit': 5})
        categories = client.get_categories({'limit': 5})
        
        print(f"📰 Found {articles['meta']['totalItems']} articles")
        print(f"🗂️ Found {categories['meta']['totalItems']} categories")
        
        if not articles['data']:
            print('⚠️ No articles found. Please create some articles first.')
            return
            
        if not categories['data']:
            print('⚠️ No categories found. Please create some categories first.')
            return
        
        first_article = articles['data'][0]
        first_category = categories['data'][0]
        
        print(f"\n📰 First Article: \"{first_article['title']}\" (ID: {first_article['id']})")
        print(f"🗂️ First Category: \"{first_category['name']}\" (ID: {first_category['id']})")
        
        print('\n' + '=' * 50)
        print('🔧 RUNNING UPDATE EXAMPLES')
        print('=' * 50)
        
        # 3. Update Article Example
        print('\n📰 Example 1: Update Article')
        current_time = datetime.now().isoformat()
        article_update_data = {
            'title': f"{first_article['title']} - Updated {current_time}",
            'excerpt': 'This article has been updated with new information.',
            'tags': ['updated', 'example', 'test', 'python'],
            'priority': 8,
            'isFeatured': True,
            'metaTitle': 'Updated Article - Python Test Example',
            'metaDescription': 'This is an example of updating an article via Python API client'
        }
        
        updated_article = client.update_article(first_article['id'], article_update_data)
        print(f"✅ Updated title: \"{updated_article['title']}\"")
        print(f"✅ Updated priority: {updated_article['priority']}")
        print(f"✅ Featured: {updated_article['isFeatured']}")
        
        # 4. Update Category Example
        print('\n🗂️ Example 2: Update Category')
        category_update_data = {
            'name': f"{first_category['name']} - Updated",
            'description': f"Updated description for {first_category['name']} at {current_time}",
            'color': '#4ECDC4',
            'sortOrder': 2,
            'isActive': True,
            'isPublic': True
        }
        
        updated_category = client.update_category(first_category['id'], category_update_data)
        print(f"✅ Updated name: \"{updated_category['name']}\"")
        print(f"✅ Updated color: {updated_category['color']}")
        
        # 5. Publish Article Example
        if first_article['status'] != 'published':
            print('\n📢 Example 3: Publish Article')
            published_article = client.publish_article(first_article['id'])
            print(f"✅ Article published at: {published_article['publishedAt']}")
        else:
            print('\n📢 Example 3: Article already published')
        
        # 6. Reorder Categories Example
        if len(categories['data']) >= 2:
            print('\n🔄 Example 4: Reorder Categories')
            reorder_data = [
                {'id': cat['id'], 'sortOrder': idx + 1}
                for idx, cat in enumerate(categories['data'][:2])
            ]
            
            client.reorder_categories(reorder_data)
            print('✅ Categories reordered successfully')
        
        print('\n' + '=' * 50)
        print('🎉 ALL EXAMPLES COMPLETED SUCCESSFULLY!')
        print('=' * 50)
        
    except Exception as e:
        print(f'\n❌ Example failed: {e}')
        raise

def example_update_article_full(client: NewsUpdateClient, article_id: int) -> Dict:
    """Example: Update article with full data"""
    return client.update_article(article_id, {
        'title': 'Messi Signs New Contract with Barcelona - Python Update',
        'excerpt': 'Lionel Messi has officially signed a new 3-year contract with FC Barcelona.',
        'content': '<h1>Messi Returns</h1><p>In a surprising turn of events...</p>',
        'tags': ['messi', 'barcelona', 'contract', 'transfer', '2025', 'python'],
        'status': 'published',
        'publishedAt': datetime.now().isoformat(),
        'isFeatured': True,
        'priority': 9,
        'categoryId': 1,
        'relatedLeagueId': 140,
        'relatedTeamId': 529,
        'relatedPlayerId': 154,
        'featuredImage': 'https://example.com/images/messi-barcelona-2025.jpg',
        'metaTitle': 'Messi Returns to Barcelona - Official Contract Signing 2025',
        'metaDescription': 'Breaking news: Lionel Messi signs new 3-year contract with Barcelona.'
    })

def example_update_category_full(client: NewsUpdateClient, category_id: int) -> Dict:
    """Example: Update category with full data"""
    return client.update_category(category_id, {
        'name': 'Transfer News & Rumors - Python Updated',
        'description': 'Latest transfer news, rumors, confirmations and market updates',
        'icon': 'transfer-icon-python',
        'color': '#FF6B35',
        'sortOrder': 1,
        'isActive': True,
        'isPublic': True,
        'metaTitle': 'Transfer News - Latest Football Transfers & Rumors',
        'metaDescription': 'Stay updated with the latest football transfer news and rumors.'
    })

def example_partial_updates():
    """Example: Partial update operations"""
    client = NewsUpdateClient()
    client.login()
    
    # Get first article and category
    articles = client.get_articles({'limit': 1})
    categories = client.get_categories({'limit': 1})
    
    if articles['data'] and categories['data']:
        article_id = articles['data'][0]['id']
        category_id = categories['data'][0]['id']
        
        # Partial article update
        print('📰 Partial Article Update:')
        client.update_article(article_id, {
            'title': 'Updated Title Only - Python',
            'priority': 6
        })
        
        # Partial category update
        print('🗂️ Partial Category Update:')
        client.update_category(category_id, {
            'sortOrder': 15,
            'color': '#9B59B6'
        })

if __name__ == '__main__':
    try:
        run_examples()
    except KeyboardInterrupt:
        print('\n👋 Script interrupted by user')
    except Exception as e:
        print(f'\n💥 Script failed: {e}')
        exit(1)
