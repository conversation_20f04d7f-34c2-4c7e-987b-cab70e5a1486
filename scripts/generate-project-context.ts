// scripts/generate-project-context.ts
import * as fs from 'fs';
import * as path from 'path';

const FEATURES_PATH = './src/features';
const LAYOUT_PATH = './src/shared/components/layout';

function getVersions(dir: string): string[] {
  if (!fs.existsSync(dir)) return [];
  return fs
    .readdirSync(dir)
    .filter((name) => name.startsWith('v') && fs.statSync(path.join(dir, name)).isDirectory());
}

function generateFeatureContext() {
  const features: Record<string, any> = {};
  if (!fs.existsSync(FEATURES_PATH)) return features;

  for (const feature of fs.readdirSync(FEATURES_PATH)) {
    const featurePath = path.join(FEATURES_PATH, feature);
    if (!fs.statSync(featurePath).isDirectory()) continue;

    const pagesPath = path.join(featurePath, 'pages');
    const componentsPath = path.join(featurePath, 'components');

    const pages: Record<string, string> = {};
    const components: Record<string, string> = {};

    if (fs.existsSync(pagesPath)) {
      for (const page of fs.readdirSync(pagesPath)) {
        const pageDir = path.join(pagesPath, page);
        const versions = getVersions(pageDir);
        if (versions.length > 0) {
          pages[page] = versions[0];
        }
      }
    }

    if (fs.existsSync(componentsPath)) {
      for (const comp of fs.readdirSync(componentsPath)) {
        const compDir = path.join(componentsPath, comp);
        const versions = getVersions(compDir);
        if (versions.length > 0) {
          components[comp] = versions[0];
        }
      }
    }

    features[feature] = { pages, components };
  }

  return features;
}

function generateLayoutContext() {
  const parts = ['header', 'footer', 'body'];
  const layout: Record<string, any> = {};

  for (const part of parts) {
    const partPath = path.join(LAYOUT_PATH, part);
    const versions = getVersions(partPath);
    layout[part] = versions[0] || null;

    if (part === 'body' && versions.includes('v1')) {
      const sectionsPath = path.join(partPath, 'v1');
      const sections = fs.existsSync(sectionsPath)
        ? fs.readdirSync(sectionsPath).filter((s) => fs.statSync(path.join(sectionsPath, s)).isDirectory())
        : [];

      const bodySections: Record<string, string> = {};
      for (const section of sections) {
        const sectionDir = path.join(sectionsPath, section);
        const sectionVersions = getVersions(sectionDir);
        if (sectionVersions.length > 0) {
          bodySections[section] = sectionVersions[0];
        }
      }

      layout.body = {
        version: 'v1',
        sections: bodySections,
      };
    }
  }

  return layout;
}

// 🔧 Generate context
const context = {
  activeFeatures: fs.existsSync(FEATURES_PATH) ? fs.readdirSync(FEATURES_PATH) : [],
  layout: generateLayoutContext(),
  features: generateFeatureContext(),
};

// ✨ Save to file
fs.writeFileSync(
  './src/project-context.ts',
  `export const projectContext = ${JSON.stringify(context, null, 2)} as const;\n`
);

console.log('✅ project-context.ts generated!');