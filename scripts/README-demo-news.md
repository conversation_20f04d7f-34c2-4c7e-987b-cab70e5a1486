# 📰 Demo News & Categories Setup

Hướng dẫn tạo demo data cho hệ thống News với 5 categories và 20 bài viết mẫu.

## 🚀 Cách sử dụng nhanh

### 1. Tạo demo data
```bash
# Chạy script tự động
bash scripts/create-demo-news.sh
```

### 2. Xóa demo data (nếu cần)
```bash
# Xóa tất cả demo data
bash scripts/clean-demo-news.sh
```

## 📋 Chi tiết demo data

### Categories (5 categories)
1. **Bóng đá Việt Nam** - `bong-da-viet-nam`
2. **Bóng đá quốc tế** - `bong-da-quoc-te`
3. **Chuyển nhượng** - `chuyen-nhuong`
4. **Kết quả trận đấu** - `ket-qua-tran-dau`
5. **Phóng sự & Phân tích** - `phong-su-bong-da`

### Articles (20 articles, 4 per category)
- Nội dung tiếng Việt chất lượng cao
- Lấy ý tưởng từ VnExpress.net
- C<PERSON> đầy đủ SEO fields, tags, featured images
- Một số bài có related league/team data

## 🔧 Cách chạy thủ công

### Nếu script không hoạt động:

```bash
# 1. Kiểm tra database
sudo -u postgres psql -d testlivesport -c "SELECT 1;"

# 2. Chạy SQL file trực tiếp
sudo -u postgres psql -d testlivesport -f scripts/vnexpress-sample-data.sql

# 3. Kiểm tra kết quả
sudo -u postgres psql -d testlivesport -c "SELECT COUNT(*) FROM news_categories;"
sudo -u postgres psql -d testlivesport -c "SELECT COUNT(*) FROM news_articles;"
```

## 🧪 Test endpoints

### Public endpoints
```bash
# Danh sách categories
curl "http://localhost:3000/news/categories"

# Bài viết cụ thể (có related data)
curl "http://localhost:3000/news/doi-tuyen-viet-nam-chuan-bi-vong-loai-world-cup-2026"

# Danh sách bài viết theo category
curl "http://localhost:3000/news/categories/bong-da-viet-nam/articles"
```

### Admin endpoints (cần token)
```bash
# Login để lấy token
TOKEN=$(curl -s -X POST "http://localhost:3000/system-auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123456"}' | \
  jq -r '.accessToken')

# Danh sách articles
curl "http://localhost:3000/admin/news/articles?limit=10" \
  -H "Authorization: Bearer $TOKEN"

# Danh sách categories
curl "http://localhost:3000/admin/news/categories" \
  -H "Authorization: Bearer $TOKEN"
```

## 🔍 Troubleshooting

### Lỗi database connection
```bash
# Kiểm tra PostgreSQL service
sudo systemctl status postgresql

# Kiểm tra database tồn tại
sudo -u postgres psql -l | grep testlivesport

# Kiểm tra .env file
cat .env | grep DB_
```

### Lỗi permission
```bash
# Đảm bảo script có quyền execute
chmod +x scripts/create-demo-news.sh
chmod +x scripts/clean-demo-news.sh
```

### Lỗi import SQL
```bash
# Kiểm tra file SQL tồn tại
ls -la scripts/vnexpress-sample-data.sql

# Chạy với verbose để xem lỗi chi tiết
sudo -u postgres psql -d testlivesport -f scripts/vnexpress-sample-data.sql -v ON_ERROR_STOP=1
```

## 📱 Swagger UI

Sau khi tạo demo data, bạn có thể test qua Swagger UI:
- URL: http://localhost:3000/api
- Tìm section "News" để test các endpoints

## 🎯 Mục đích sử dụng

Demo data này phù hợp cho:
- ✅ Development và testing
- ✅ Demo cho client
- ✅ Frontend development
- ✅ API testing
- ✅ Performance testing

**⚠️ Lưu ý:** Không sử dụng demo data này trên production!
