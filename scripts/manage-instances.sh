#!/bin/bash

# APISportsGame Instance Management Script
# Usage: ./scripts/manage-instances.sh [command] [instance]

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Instance definitions
API_3000="api-sports-game-3000"
API_3001="api-sports-game-3001"
WORKER="auto-update-sports-game"

show_help() {
    echo -e "${CYAN}🔧 APISportsGame Instance Management${NC}"
    echo "===================================="
    echo ""
    echo -e "${YELLOW}Usage:${NC} $0 [command] [instance]"
    echo ""
    echo -e "${BLUE}Commands:${NC}"
    echo "  start [instance]    - Start specific instance or all"
    echo "  stop [instance]     - Stop specific instance or all"
    echo "  restart [instance]  - Restart specific instance or all"
    echo "  status              - Show all instances status"
    echo "  logs [instance]     - Show logs for specific instance"
    echo "  monitor             - Real-time monitoring"
    echo "  ports               - Show port usage"
    echo "  test                - Test all API endpoints"
    echo ""
    echo -e "${BLUE}Instances:${NC}"
    echo "  api-3000           - API Server on port 3000"
    echo "  api-3001           - API Server on port 3001"
    echo "  worker             - Background worker service"
    echo "  all                - All instances (default)"
    echo ""
    echo -e "${BLUE}Examples:${NC}"
    echo "  $0 start api-3000   # Start only API instance on port 3000"
    echo "  $0 stop api-3001    # Stop only API instance on port 3001"
    echo "  $0 restart worker   # Restart only worker service"
    echo "  $0 start            # Start all instances"
}

get_instance_name() {
    case "$1" in
        "api-3000") echo "$API_3000" ;;
        "api-3001") echo "$API_3001" ;;
        "worker") echo "$WORKER" ;;
        *) echo "all" ;;
    esac
}

start_instance() {
    local instance=$(get_instance_name "$1")
    
    if [ "$instance" = "all" ]; then
        echo -e "${GREEN}🚀 Starting all instances...${NC}"
        pm2 start ecosystem.config.js
    else
        echo -e "${GREEN}🚀 Starting $instance...${NC}"
        pm2 start ecosystem.config.js --only "$instance"
    fi
}

stop_instance() {
    local instance=$(get_instance_name "$1")
    
    if [ "$instance" = "all" ]; then
        echo -e "${RED}🛑 Stopping all instances...${NC}"
        pm2 stop all
    else
        echo -e "${RED}🛑 Stopping $instance...${NC}"
        pm2 stop "$instance"
    fi
}

restart_instance() {
    local instance=$(get_instance_name "$1")
    
    if [ "$instance" = "all" ]; then
        echo -e "${YELLOW}🔄 Restarting all instances...${NC}"
        pm2 restart all
    else
        echo -e "${YELLOW}🔄 Restarting $instance...${NC}"
        pm2 restart "$instance"
    fi
}

show_status() {
    echo -e "${BLUE}📊 Instance Status${NC}"
    echo "=================="
    pm2 list
    echo ""
    echo -e "${BLUE}🌐 Port Usage${NC}"
    echo "============="
    ss -tlnp | grep -E ":300[0-9]" || echo "No ports in 300x range found"
}

show_logs() {
    local instance=$(get_instance_name "$1")
    
    if [ "$instance" = "all" ]; then
        echo -e "${PURPLE}📋 All Instance Logs${NC}"
        pm2 logs --lines 20
    else
        echo -e "${PURPLE}📋 Logs for $instance${NC}"
        pm2 logs "$instance" --lines 20
    fi
}

monitor_instances() {
    echo -e "${CYAN}📈 Real-time Monitoring${NC}"
    echo "Press Ctrl+C to exit"
    pm2 monit
}

show_ports() {
    echo -e "${BLUE}🌐 Port Status${NC}"
    echo "=============="
    echo ""
    
    # Check port 3000
    if ss -tln | grep -q ":3000 "; then
        echo -e "Port 3000: ${GREEN}✅ LISTENING${NC} (api-sports-game-3000)"
    else
        echo -e "Port 3000: ${RED}❌ NOT LISTENING${NC}"
    fi
    
    # Check port 3001
    if ss -tln | grep -q ":3001 "; then
        echo -e "Port 3001: ${GREEN}✅ LISTENING${NC} (api-sports-game-3001)"
    else
        echo -e "Port 3001: ${RED}❌ NOT LISTENING${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}Detailed port information:${NC}"
    ss -tlnp | grep -E ":300[0-9]"
}

test_endpoints() {
    echo -e "${CYAN}🧪 Testing API Endpoints${NC}"
    echo "========================"
    
    # Test port 3000
    echo -n "Port 3000: "
    if curl -s -f http://localhost:3000/football/leagues?limit=1 > /dev/null; then
        echo -e "${GREEN}✅ Working${NC}"
    else
        echo -e "${RED}❌ Failed${NC}"
    fi
    
    # Test port 3001
    echo -n "Port 3001: "
    if curl -s -f http://localhost:3001/football/leagues?limit=1 > /dev/null; then
        echo -e "${GREEN}✅ Working${NC}"
    else
        echo -e "${RED}❌ Failed${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}📊 Response Time Test${NC}"
    echo "Port 3000: $(curl -s -w "%{time_total}s" -o /dev/null http://localhost:3000/football/leagues?limit=1)"
    echo "Port 3001: $(curl -s -w "%{time_total}s" -o /dev/null http://localhost:3001/football/leagues?limit=1)"
}

# Main script logic
case "$1" in
    "start")
        start_instance "$2"
        ;;
    "stop")
        stop_instance "$2"
        ;;
    "restart")
        restart_instance "$2"
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    "monitor")
        monitor_instances
        ;;
    "ports")
        show_ports
        ;;
    "test")
        test_endpoints
        ;;
    *)
        show_help
        ;;
esac
