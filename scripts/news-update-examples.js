#!/usr/bin/env node

/**
 * News System Update Examples
 * 
 * This script demonstrates how to update News Articles and Categories
 * using the APISportsGame News System API.
 * 
 * Usage:
 *   node scripts/news-update-examples.js
 * 
 * Requirements:
 *   npm install axios
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:3000';
const ADMIN_CREDENTIALS = {
    username: 'admin',
    password: 'admin123456'
};

class NewsUpdateClient {
    constructor() {
        this.token = null;
        this.baseURL = API_BASE_URL;
    }

    /**
     * Login and get JWT token
     */
    async login() {
        try {
            console.log('🔐 Logging in...');
            const response = await axios.post(`${this.baseURL}/system-auth/login`, ADMIN_CREDENTIALS);
            this.token = response.data.access_token;
            console.log('✅ Login successful!');
            console.log(`👤 User: ${response.data.user.username} (${response.data.user.role})`);
            return this.token;
        } catch (error) {
            console.error('❌ Login failed:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Get headers with authorization
     */
    getHeaders() {
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
        };
    }

    /**
     * Update News Article
     */
    async updateArticle(articleId, updateData) {
        try {
            console.log(`📰 Updating article ID: ${articleId}`);
            const response = await axios.patch(
                `${this.baseURL}/admin/news/articles/${articleId}`,
                updateData,
                { headers: this.getHeaders() }
            );
            console.log('✅ Article updated successfully!');
            return response.data;
        } catch (error) {
            console.error('❌ Article update failed:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Update News Category
     */
    async updateCategory(categoryId, updateData) {
        try {
            console.log(`🗂️ Updating category ID: ${categoryId}`);
            const response = await axios.patch(
                `${this.baseURL}/admin/news/categories/${categoryId}`,
                updateData,
                { headers: this.getHeaders() }
            );
            console.log('✅ Category updated successfully!');
            return response.data;
        } catch (error) {
            console.error('❌ Category update failed:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Publish Article
     */
    async publishArticle(articleId) {
        try {
            console.log(`📢 Publishing article ID: ${articleId}`);
            const response = await axios.post(
                `${this.baseURL}/admin/news/articles/${articleId}/publish`,
                {},
                { headers: this.getHeaders() }
            );
            console.log('✅ Article published successfully!');
            return response.data;
        } catch (error) {
            console.error('❌ Article publish failed:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Reorder Categories
     */
    async reorderCategories(orders) {
        try {
            console.log('🔄 Reordering categories...');
            const response = await axios.post(
                `${this.baseURL}/admin/news/categories/reorder`,
                orders,
                { headers: this.getHeaders() }
            );
            console.log('✅ Categories reordered successfully!');
            return response.data;
        } catch (error) {
            console.error('❌ Category reorder failed:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Get Articles List
     */
    async getArticles(params = {}) {
        try {
            const response = await axios.get(
                `${this.baseURL}/admin/news/articles`,
                { 
                    headers: this.getHeaders(),
                    params: params
                }
            );
            return response.data;
        } catch (error) {
            console.error('❌ Get articles failed:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Get Categories List
     */
    async getCategories(params = {}) {
        try {
            const response = await axios.get(
                `${this.baseURL}/admin/news/categories`,
                { 
                    headers: this.getHeaders(),
                    params: params
                }
            );
            return response.data;
        } catch (error) {
            console.error('❌ Get categories failed:', error.response?.data || error.message);
            throw error;
        }
    }
}

/**
 * Example Update Operations
 */
async function runExamples() {
    const client = new NewsUpdateClient();

    try {
        // 1. Login
        await client.login();

        console.log('\n' + '='.repeat(50));
        console.log('📋 GETTING CURRENT DATA');
        console.log('='.repeat(50));

        // 2. Get current articles and categories
        const articles = await client.getArticles({ limit: 5 });
        const categories = await client.getCategories({ limit: 5 });

        console.log(`📰 Found ${articles.meta.totalItems} articles`);
        console.log(`🗂️ Found ${categories.meta.totalItems} categories`);

        if (articles.data.length === 0) {
            console.log('⚠️ No articles found. Please create some articles first.');
            return;
        }

        if (categories.data.length === 0) {
            console.log('⚠️ No categories found. Please create some categories first.');
            return;
        }

        const firstArticle = articles.data[0];
        const firstCategory = categories.data[0];

        console.log(`\n📰 First Article: "${firstArticle.title}" (ID: ${firstArticle.id})`);
        console.log(`🗂️ First Category: "${firstCategory.name}" (ID: ${firstCategory.id})`);

        console.log('\n' + '='.repeat(50));
        console.log('🔧 RUNNING UPDATE EXAMPLES');
        console.log('='.repeat(50));

        // 3. Update Article Example
        console.log('\n📰 Example 1: Update Article');
        const articleUpdateData = {
            title: `${firstArticle.title} - Updated ${new Date().toISOString()}`,
            excerpt: 'This article has been updated with new information.',
            tags: ['updated', 'example', 'test'],
            priority: 7,
            isFeatured: true,
            metaTitle: 'Updated Article - Test Example',
            metaDescription: 'This is an example of updating an article via API'
        };

        const updatedArticle = await client.updateArticle(firstArticle.id, articleUpdateData);
        console.log(`✅ Updated title: "${updatedArticle.title}"`);
        console.log(`✅ Updated priority: ${updatedArticle.priority}`);
        console.log(`✅ Featured: ${updatedArticle.isFeatured}`);

        // 4. Update Category Example
        console.log('\n🗂️ Example 2: Update Category');
        const categoryUpdateData = {
            name: `${firstCategory.name} - Updated`,
            description: `Updated description for ${firstCategory.name} at ${new Date().toISOString()}`,
            color: '#FF6B35',
            sortOrder: 1,
            isActive: true,
            isPublic: true
        };

        const updatedCategory = await client.updateCategory(firstCategory.id, categoryUpdateData);
        console.log(`✅ Updated name: "${updatedCategory.name}"`);
        console.log(`✅ Updated color: ${updatedCategory.color}`);

        // 5. Publish Article Example
        if (firstArticle.status !== 'published') {
            console.log('\n📢 Example 3: Publish Article');
            const publishedArticle = await client.publishArticle(firstArticle.id);
            console.log(`✅ Article published at: ${publishedArticle.publishedAt}`);
        } else {
            console.log('\n📢 Example 3: Article already published');
        }

        // 6. Reorder Categories Example
        if (categories.data.length >= 2) {
            console.log('\n🔄 Example 4: Reorder Categories');
            const reorderData = categories.data.slice(0, 2).map((cat, index) => ({
                id: cat.id,
                sortOrder: index + 1
            }));

            await client.reorderCategories(reorderData);
            console.log('✅ Categories reordered successfully');
        }

        console.log('\n' + '='.repeat(50));
        console.log('🎉 ALL EXAMPLES COMPLETED SUCCESSFULLY!');
        console.log('='.repeat(50));

    } catch (error) {
        console.error('\n❌ Example failed:', error.message);
        process.exit(1);
    }
}

/**
 * Individual Update Examples
 */
const examples = {
    // Update article with full data
    updateArticleFull: async (client, articleId) => {
        return await client.updateArticle(articleId, {
            title: 'Messi Signs New Contract with Barcelona - Complete Update',
            excerpt: 'Lionel Messi has officially signed a new 3-year contract with FC Barcelona.',
            content: '<h1>Messi Returns</h1><p>In a surprising turn of events...</p>',
            tags: ['messi', 'barcelona', 'contract', 'transfer', '2025'],
            status: 'published',
            publishedAt: new Date().toISOString(),
            isFeatured: true,
            priority: 9,
            categoryId: 1,
            relatedLeagueId: 140,
            relatedTeamId: 529,
            relatedPlayerId: 154,
            featuredImage: 'https://example.com/images/messi-barcelona-2025.jpg',
            metaTitle: 'Messi Returns to Barcelona - Official Contract Signing 2025',
            metaDescription: 'Breaking news: Lionel Messi signs new 3-year contract with Barcelona.'
        });
    },

    // Update category with full data
    updateCategoryFull: async (client, categoryId) => {
        return await client.updateCategory(categoryId, {
            name: 'Transfer News & Rumors',
            description: 'Latest transfer news, rumors, confirmations and market updates',
            icon: 'transfer-icon-v2',
            color: '#FF6B35',
            sortOrder: 1,
            isActive: true,
            isPublic: true,
            metaTitle: 'Transfer News - Latest Football Transfers & Rumors',
            metaDescription: 'Stay updated with the latest football transfer news and rumors.'
        });
    },

    // Partial update examples
    updateArticlePartial: async (client, articleId) => {
        return await client.updateArticle(articleId, {
            title: 'Updated Title Only',
            priority: 5
        });
    },

    updateCategoryPartial: async (client, categoryId) => {
        return await client.updateCategory(categoryId, {
            sortOrder: 10,
            isActive: false
        });
    }
};

// Export for use in other scripts
module.exports = { NewsUpdateClient, examples };

// Run examples if script is executed directly
if (require.main === module) {
    runExamples().catch(console.error);
}
