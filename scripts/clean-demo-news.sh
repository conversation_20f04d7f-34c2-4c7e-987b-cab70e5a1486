#!/bin/bash

# =====================================================
# SCRIPT XÓA DEMO NEWS & CATEGORIES
# =====================================================
# Script này xóa demo data để reset về trạng thái ban đầu
# Sử dụng: bash scripts/clean-demo-news.sh

echo "🧹 Bắt đầu xóa demo news và categories..."

# Xác nhận từ user
read -p "⚠️  Bạn có chắc muốn xóa tất cả demo data? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Hủy bỏ thao tác"
    exit 0
fi

# Kiểm tra database connection
echo "📡 Kiểm tra kết nối database..."
if ! sudo -u postgres psql -d testlivesport -c "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ Không thể kết nối database 'testlivesport'"
    exit 1
fi

echo "✅ Database connection OK"

# Xóa demo articles
echo "🗑️  Đang xóa demo articles..."
sudo -u postgres psql -d testlivesport -c "
DELETE FROM news_articles 
WHERE \"categoryId\" IN (
    SELECT id FROM news_categories 
    WHERE slug IN ('bong-da-viet-nam', 'bong-da-quoc-te', 'chuyen-nhuong', 'ket-qua-tran-dau', 'phong-su-bong-da')
);
"

# Xóa demo categories
echo "🗑️  Đang xóa demo categories..."
sudo -u postgres psql -d testlivesport -c "
DELETE FROM news_categories 
WHERE slug IN ('bong-da-viet-nam', 'bong-da-quoc-te', 'chuyen-nhuong', 'ket-qua-tran-dau', 'phong-su-bong-da');
"

# Kiểm tra kết quả
REMAINING_DEMO_CATEGORIES=$(sudo -u postgres psql -d testlivesport -t -c "SELECT COUNT(*) FROM news_categories WHERE slug IN ('bong-da-viet-nam', 'bong-da-quoc-te', 'chuyen-nhuong', 'ket-qua-tran-dau', 'phong-su-bong-da');" | xargs)

REMAINING_DEMO_ARTICLES=$(sudo -u postgres psql -d testlivesport -t -c "SELECT COUNT(*) FROM news_articles WHERE title LIKE '%Đội tuyển Việt Nam%' OR title LIKE '%Messi%' OR title LIKE '%Mbappe%';" | xargs)

echo "📊 Kết quả:"
echo "   - Demo categories còn lại: $REMAINING_DEMO_CATEGORIES"
echo "   - Demo articles còn lại: $REMAINING_DEMO_ARTICLES"

if [ "$REMAINING_DEMO_CATEGORIES" -eq 0 ] && [ "$REMAINING_DEMO_ARTICLES" -eq 0 ]; then
    echo "✅ THÀNH CÔNG! Đã xóa sạch demo data"
else
    echo "⚠️  Vẫn còn một số demo data chưa được xóa"
fi

echo "✨ Hoàn thành!"
