module.exports = {
    apps: [
        // ✅ API Instance 1 - Port 3000
        {
            name: 'api-sports-game-3000',
            script: 'dist/src/main.js',
            env: {
                APP_TYPE: 'api',
                TZ: 'UTC',
                NODE_TZ: 'UTC',
                NODE_ENV: 'production',
                PORT: 3000, // Explicit port control
                HOST: '127.0.0.1', // ✅ LOCALHOST ONLY
                INSTANCE_ID: 'api-3000', // Instance identifier
            },
            instances: 1, // Single instance per config
            exec_mode: 'fork', // Fork mode for explicit control
            watch: false,
            max_memory_restart: '2G',

            // ✅ MINIMAL LOGS - Only errors
            log_file: './logs/api-3000-combined.log',
            out_file: './logs/api-3000-out.log',
            error_file: './logs/api-3000-error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            max_log_size: '10M',
            retain_logs: 5,
            log_type: 'json',
        },

        // ✅ API Instance 2 - Port 3001
        {
            name: 'api-sports-game-3001',
            script: 'dist/src/main.js',
            env: {
                APP_TYPE: 'api',
                TZ: 'UTC',
                NODE_TZ: 'UTC',
                NODE_ENV: 'production',
                PORT: 3001, // Explicit port control
                HOST: '127.0.0.1', // ✅ LOCALHOST ONLY
                INSTANCE_ID: 'api-3001', // Instance identifier
            },
            instances: 1, // Single instance per config
            exec_mode: 'fork', // Fork mode for explicit control
            watch: false,
            max_memory_restart: '2G',

            // ✅ MINIMAL LOGS - Only errors
            log_file: './logs/api-3001-combined.log',
            out_file: './logs/api-3001-out.log',
            error_file: './logs/api-3001-error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            max_log_size: '10M',
            retain_logs: 5,
            log_type: 'json',
        },

        // ✅ Worker Service - Background Tasks
        {
            name: 'auto-update-sports-game',
            script: 'dist/src/worker.js',
            env: {
                APP_TYPE: 'worker',
                TZ: 'UTC',
                NODE_TZ: 'UTC',
                NODE_ENV: 'development', // Changed to development for debugging
                INSTANCE_ID: 'worker-main', // Worker identifier
            },
            instances: 1, // Single worker instance
            exec_mode: 'fork',
            watch: false,
            max_memory_restart: '3G',

            // ✅ WORKER MINIMAL LOGS - Only errors
            log_file: './logs/worker-combined.log',
            out_file: './logs/worker-out.log',
            error_file: './logs/worker-error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            max_log_size: '5M',
            retain_logs: 3,
            log_type: 'json',
        },
    ],
};