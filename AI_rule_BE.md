# 🧠 Augmented Agent Coding Rules for NestJS (AI-Compatible Clean Architecture)

This rule set combines Clean Architecture best practices (inspired by [cursor.directory/clean-nestjs-typescript-cursor-rules](https://cursor.directory/clean-nestjs-typescript-cursor-rules)) and all previous principles we've discussed in this conversation, optimized for use with AI Agents in a NestJS backend codebase.

---

## ⚙️ 1. Clean Architecture for NestJS

### Folder Structure (Modular + Layered)

```
src/
├── modules/
│   ├── user/
│   │   ├── application/        # Use cases / services
│   │   ├── domain/             # Entities, interfaces
│   │   ├── infrastructure/     # TypeORM entities, DB services
│   │   ├── presentation/       # Controllers, DTOs, validators
│   │   ├── user.module.ts      # Module registration
│   │   └── index.ts            # Public API
│   └── auth/
├── shared/                    # Reusable components across modules
│   ├── configs/
│   ├── guards/
│   ├── interceptors/
│   ├── decorators/
│   ├── utils/
│   ├── services/
│   └── validators/
├── project-context.ts         # AI context memory file (auto-generated)
```

> 📌 Each module must be **independent**, follow domain boundaries, and be version-isolated if needed.

---

## 🧱 2. Naming Conventions

| Element   | Convention          |
| --------- | ------------------- |
| Folders   | kebab-case          |
| Files     | kebab-case          |
| Classes   | PascalCase          |
| Functions | camelCase           |
| Constants | UPPER\_SNAKE\_CASE  |
| DTOs      | PascalCase + Dto    |
| Entities  | Singular PascalCase |

> 🔁 All versions must live inside a `v1/`, `v2/` directory, **not in file names**.

---

## 🔄 3. Module Communication

* ❌ DO NOT call services across modules directly.
* ✅ Use one of the following communication patterns:

  * Interfaces with dependency injection (domain contracts)
  * Events (`EventEmitter2`, domain events)
  * Message queues (e.g., BullMQ, RabbitMQ)
  * Internal HTTP/gRPC APIs (for microservices)

---

## 🧠 4. AI Memory Context: `project-context.ts`

```ts
export const projectContext = {
  activeModules: ['auth', 'user', 'contract'],
  moduleVersions: {
    'user.controller': 'v1',
    'auth.service': 'v2'
  }
} as const;
```

* This file must be generated and kept up to date via script
* Agent AI must **read this before generating, importing, or modifying code**

---

## 🧩 5. Service Layer Rules

* Always use `@Injectable()`
* Wrap all logic in `try/catch` and log errors using `Logger`
* Return typed DTO or value objects
* Business logic stays in `application/` layer, not in controller

---

## 🚪 6. Controller Layer Rules

* Validate all inputs using DTOs + `class-validator`
* Return a standard response format:

```ts
{
  data: ..., 
  message: 'success',
  meta?: { ...optional }
}
```

* Use `HttpStatus` and `HttpException` for error handling

---

## 📦 7. DTO & Validation

```ts
export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;
}
```

* Place DTOs inside `presentation/dto/`
* Do not use `any`. Always specify types.

---

## 🧱 8. Entity Rules

```ts
@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @CreateDateColumn()
  createdAt: Date;
}
```

* Entities belong in the `domain/` layer
* Use database decorators only in `infrastructure/`

---

## ✅ 9. Separation of Concerns (SoC)

| Layer           | Responsibility                     |
| --------------- | ---------------------------------- |
| presentation/   | Controllers, DTOs, Validators      |
| application/    | Use cases, service logic           |
| domain/         | Entities, domain models/interfaces |
| infrastructure/ | DB access, storage adapters        |

---

## 🔐 10. Security Standards

* Use `ConfigService` for all env access
* Use `bcrypt` for passwords
* Use `JWT` with proper `expiresIn`
* Always validate input
* Apply guards with `@UseGuards`

---

## 🧪 11. Testing Rules

| Type         | Rule                                  |
| ------------ | ------------------------------------- |
| Unit         | Service-level, use mocks              |
| Integration  | End-to-end API test with real modules |
| E2E (option) | Minimal health & permission test      |

> ✅ Test every controller and service with appropriate setup/teardown.

---

## 🛠 12. Workflow Checklist

| Step         | Must Do                                                 |
| ------------ | ------------------------------------------------------- |
| Before Code  | Check existing shared utils, validate folder versioning |
| While Coding | Respect context, conventions, split logic per layer     |
| After Coding | Update `project-context.ts`, log in `LogWorking/`       |

---

## ✅ 13. Agent AI Compliance Checklist

* [ ] Read `project-context.ts` before generating code
* [ ] Use correct version folders, not file name suffixes
* [ ] Use proper module structure: `presentation/`, `domain/`, etc.
* [ ] Avoid duplicated logic; reuse via `shared/`
* [ ] Always validate inputs using `class-validator`
* [ ] Use clean code, single responsibility, and separation of concerns

---

## 🧠 Final Note for AI Agents

> Act as a team member.
> Follow clean structure strictly.
> Never guess structure or logic — infer from `project-context.ts`.
> Always document output.
> If unclear, ask or generate clarification prompt.
