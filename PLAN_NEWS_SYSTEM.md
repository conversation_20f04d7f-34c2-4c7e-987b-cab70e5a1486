# 📰 APISportsGame News System - Detailed Implementation Plan

## 🎯 **OVERVIEW**

<PERSON><PERSON><PERSON> triển hệ thống quản lý tin tức bóng đá với category management, dựa trên kiến trúc hiện tại của APISportsGame.

## 🏗️ **ARCHITECTURE ANALYSIS**

### **Current Strengths:**
- ✅ Robust authentication system (SystemUser + RegisteredUser)
- ✅ Well-structured module pattern
- ✅ Comprehensive caching with Redis
- ✅ Image upload system ready
- ✅ Swagger documentation
- ✅ TypeORM with PostgreSQL
- ✅ Validation with class-validator

### **Reusable Patterns:**
- ✅ **BroadcastLink pattern**: Entity-Service-Controller with auth
- ✅ **Upload pattern**: File management with categories
- ✅ **Football pattern**: Complex data relationships
- ✅ **Auth pattern**: Role-based access control

## 📊 **DATABASE DESIGN**

### **1. NewsCategory Entity:**
```typescript
@Entity('news_categories')
export class NewsCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  slug: string; // 'transfer-news', 'match-reports', etc.

  @Column()
  name: string; // 'Transfer News', 'Match Reports'

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  icon: string; // Icon URL or name

  @Column({ nullable: true })
  color: string; // Hex color for UI

  @Column({ default: 0 })
  sortOrder: number; // Display order

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: true })
  isPublic: boolean; // Show on public site

  // SEO fields
  @Column({ nullable: true })
  metaTitle: string;

  @Column({ type: 'text', nullable: true })
  metaDescription: string;

  // Relationships
  @OneToMany(() => NewsArticle, article => article.category)
  articles: NewsArticle[];

  // Audit fields
  @Column()
  createdBy: number;

  @Column({ nullable: true })
  updatedBy: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### **2. NewsArticle Entity:**
```typescript
@Entity('news_articles')
export class NewsArticle {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column({ unique: true })
  slug: string; // Auto-generated from title

  @Column({ type: 'text', nullable: true })
  excerpt: string; // Short description

  @Column({ type: 'text' })
  content: string; // Full article content (HTML)

  @Column({ nullable: true })
  featuredImage: string; // Main image URL

  @Column('simple-array', { nullable: true })
  tags: string[]; // ['messi', 'barcelona', 'transfer']

  // Status management
  @Column({ 
    type: 'enum', 
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  })
  status: 'draft' | 'published' | 'archived';

  @Column({ nullable: true })
  publishedAt: Date;

  // SEO fields
  @Column({ nullable: true })
  metaTitle: string;

  @Column({ type: 'text', nullable: true })
  metaDescription: string;

  // Football-specific fields
  @Column({ nullable: true })
  relatedLeagueId: number; // Link to League entity

  @Column({ nullable: true })
  relatedTeamId: number; // Link to Team entity

  @Column({ nullable: true })
  relatedPlayerId: number; // Link to Player entity

  @Column({ nullable: true })
  relatedFixtureId: number; // Link to Fixture entity

  // Analytics
  @Column({ default: 0 })
  viewCount: number;

  @Column({ default: 0 })
  shareCount: number;

  // Relationships
  @ManyToOne(() => NewsCategory, category => category.articles)
  @JoinColumn({ name: 'categoryId' })
  category: NewsCategory;

  @Column()
  categoryId: number;

  // Audit fields
  @Column()
  authorId: number; // SystemUser who created

  @Column({ nullable: true })
  updatedBy: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## 🛠️ **IMPLEMENTATION PHASES**

### **Phase 1: NewsCategory Management (Week 1)**

#### **1.1 Entity & Migration:**
- Create NewsCategory entity
- Database migration
- Seed default categories

#### **1.2 Category Service:**
```typescript
@Injectable()
export class NewsCategoryService {
  // CRUD operations
  async createCategory(dto: CreateCategoryDto, userId: number): Promise<NewsCategory>
  async updateCategory(id: number, dto: UpdateCategoryDto, userId: number): Promise<NewsCategory>
  async deleteCategory(id: number): Promise<void>
  
  // Query operations
  async getCategories(query: GetCategoriesDto): Promise<PaginatedCategoriesResponse>
  async getCategoryById(id: number): Promise<NewsCategory>
  async getCategoryBySlug(slug: string): Promise<NewsCategory>
  
  // Public operations
  async getPublicCategories(): Promise<NewsCategory[]>
  
  // Admin operations
  async reorderCategories(orders: CategoryOrderDto[]): Promise<void>
  async toggleCategoryStatus(id: number, isActive: boolean): Promise<NewsCategory>
}
```

#### **1.3 Category Controllers:**
```typescript
// Admin Controller
@Controller('admin/news/categories')
@UseGuards(SystemAuthGuard)
@ApiBearerAuth()
export class NewsCategoryController {
  @Post() // Create category
  @Patch(':id') // Update category
  @Delete(':id') // Delete category
  @Get() // List with pagination
  @Get(':id') // Get by ID
  @Post('reorder') // Reorder categories
}

// Public Controller
@Controller('news/categories')
export class PublicNewsCategoryController {
  @Public()
  @Get() // Get active public categories
  
  @Public()
  @Get(':slug') // Get category by slug
}
```

### **Phase 2: NewsArticle Management (Week 2-3)**

#### **2.1 Article Service:**
```typescript
@Injectable()
export class NewsArticleService {
  // CRUD operations
  async createArticle(dto: CreateArticleDto, authorId: number): Promise<NewsArticle>
  async updateArticle(id: number, dto: UpdateArticleDto, userId: number): Promise<NewsArticle>
  async deleteArticle(id: number): Promise<void>
  
  // Query operations
  async getArticles(query: GetArticlesDto): Promise<PaginatedArticlesResponse>
  async getArticleById(id: number): Promise<NewsArticle>
  async getArticleBySlug(slug: string): Promise<NewsArticle>
  
  // Public operations
  async getPublishedArticles(query: PublicArticlesDto): Promise<PaginatedArticlesResponse>
  async getArticlesByCategory(categorySlug: string, query: PublicArticlesDto): Promise<PaginatedArticlesResponse>
  async getFeaturedArticles(limit: number): Promise<NewsArticle[]>
  async getRelatedArticles(articleId: number, limit: number): Promise<NewsArticle[]>
  
  // Status management
  async publishArticle(id: number): Promise<NewsArticle>
  async unpublishArticle(id: number): Promise<NewsArticle>
  async archiveArticle(id: number): Promise<NewsArticle>
  
  // Analytics
  async incrementViewCount(id: number): Promise<void>
  async incrementShareCount(id: number): Promise<void>
}
```

#### **2.2 Article Controllers:**
```typescript
// Admin Controller
@Controller('admin/news/articles')
@UseGuards(SystemAuthGuard)
@ApiBearerAuth()
export class NewsArticleController {
  @Post() // Create article
  @Patch(':id') // Update article
  @Delete(':id') // Delete article
  @Get() // List with filters
  @Get(':id') // Get by ID
  @Post(':id/publish') // Publish article
  @Post(':id/unpublish') // Unpublish article
  @Post(':id/archive') // Archive article
}

// Public Controller
@Controller('news')
export class PublicNewsArticleController {
  @Public()
  @Get() // Get published articles
  
  @Public()
  @Get('featured') // Get featured articles
  
  @Public()
  @Get('category/:categorySlug') // Get articles by category
  
  @Public()
  @Get(':slug') // Get article by slug
  
  @Public()
  @Post(':id/view') // Increment view count
  
  @Public()
  @Post(':id/share') // Increment share count
}
```

### **Phase 3: Advanced Features (Week 4)**

#### **3.1 Search & Filtering:**
- Full-text search in articles
- Filter by category, tags, date range
- Related articles algorithm
- Popular articles (by views)

#### **3.2 SEO Optimization:**
- Auto-generate meta tags
- Sitemap generation
- Schema.org markup
- Social media previews

#### **3.3 Analytics Integration:**
- View tracking
- Popular content reports
- Category performance
- Author statistics

## 🔧 **TECHNICAL SPECIFICATIONS**

### **API Endpoints:**

#### **Admin Endpoints:**
```
POST   /admin/news/categories              # Create category
GET    /admin/news/categories              # List categories
GET    /admin/news/categories/:id          # Get category
PATCH  /admin/news/categories/:id          # Update category
DELETE /admin/news/categories/:id          # Delete category
POST   /admin/news/categories/reorder      # Reorder categories

POST   /admin/news/articles                # Create article
GET    /admin/news/articles                # List articles
GET    /admin/news/articles/:id            # Get article
PATCH  /admin/news/articles/:id            # Update article
DELETE /admin/news/articles/:id            # Delete article
POST   /admin/news/articles/:id/publish    # Publish article
POST   /admin/news/articles/:id/unpublish  # Unpublish article
POST   /admin/news/articles/:id/archive    # Archive article
```

#### **Public Endpoints:**
```
GET    /news/categories                    # Get public categories
GET    /news/categories/:slug              # Get category by slug
GET    /news                               # Get published articles
GET    /news/featured                      # Get featured articles
GET    /news/category/:categorySlug        # Get articles by category
GET    /news/:slug                         # Get article by slug
POST   /news/:id/view                      # Increment view count
POST   /news/:id/share                     # Increment share count
```

### **Caching Strategy:**
```typescript
// Category cache
'news_categories_public' // TTL: 1 hour
'news_category_{slug}' // TTL: 1 hour

// Article cache
'news_articles_published_{page}_{limit}' // TTL: 5 minutes
'news_article_{slug}' // TTL: 15 minutes
'news_featured_articles' // TTL: 30 minutes
'news_category_articles_{categorySlug}_{page}' // TTL: 10 minutes
```

### **Default Categories:**
```typescript
const defaultCategories = [
  { slug: 'transfer-news', name: 'Transfer News', icon: 'transfer', color: '#FF6B35' },
  { slug: 'match-reports', name: 'Match Reports', icon: 'match', color: '#4ECDC4' },
  { slug: 'player-interviews', name: 'Player Interviews', icon: 'interview', color: '#45B7D1' },
  { slug: 'league-updates', name: 'League Updates', icon: 'league', color: '#96CEB4' },
  { slug: 'injury-news', name: 'Injury News', icon: 'medical', color: '#FFEAA7' },
  { slug: 'tactical-analysis', name: 'Tactical Analysis', icon: 'tactics', color: '#DDA0DD' },
  { slug: 'youth-football', name: 'Youth Football', icon: 'youth', color: '#98D8C8' },
  { slug: 'womens-football', name: "Women's Football", icon: 'womens', color: '#F7DC6F' },
];
```

## 🚀 **INTEGRATION POINTS**

### **1. Football Data Integration:**
- Link articles to leagues, teams, players, fixtures
- Auto-suggest related entities when creating articles
- Display related news on team/player pages

### **2. Image System Integration:**
- Reuse existing upload system for featured images
- Support multiple images per article
- Image optimization and resizing

### **3. Authentication Integration:**
- SystemUser as authors
- Role-based permissions (Admin, Moderator, Editor)
- Author attribution and profiles

### **4. Search Integration:**
- Full-text search across articles
- Category-based filtering
- Tag-based discovery

## 📈 **SUCCESS METRICS**

### **Technical Metrics:**
- API response time < 200ms
- 99.9% uptime
- Cache hit ratio > 80%
- Database query optimization

### **Business Metrics:**
- Article creation workflow efficiency
- Content categorization accuracy
- User engagement with news content
- SEO performance improvements

## 🎯 **NEXT STEPS**

1. **Review and approve** this implementation plan
2. **Create database migrations** for entities
3. **Implement Phase 1** (Categories) first
4. **Test thoroughly** with sample data
5. **Iterate based on feedback**
6. **Deploy incrementally** to production

This plan leverages existing APISportsGame patterns and provides a robust foundation for football news management with excellent scalability and maintainability.
