import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { NewsModule } from '../../src/news/news.module';
import { NewsCategory } from '../../src/news/categories/entities/news-category.entity';
import { NewsArticle, ArticleStatus } from '../../src/news/articles/entities/news-article.entity';
import { DatabaseService } from '../../src/core/database/database.service';
import { CacheService } from '../../src/core/cache/cache.service';
import configuration from '../../src/core/config/configuration';

describe('News System Integration (e2e)', () => {
    let app: INestApplication;
    let moduleFixture: TestingModule;

    // Mock CacheService to avoid Redis dependency in tests
    const mockCacheService = {
        getCache: jest.fn().mockResolvedValue(null),
        setCache: jest.fn().mockResolvedValue(undefined),
        deleteCache: jest.fn().mockResolvedValue(undefined),
        deleteCachePattern: jest.fn().mockResolvedValue(undefined),
    };

    beforeAll(async () => {
        moduleFixture = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({ load: [configuration] }),
                TypeOrmModule.forRootAsync({
                    imports: [ConfigModule],
                    useClass: DatabaseService,
                }),
                NewsModule,
            ],
        })
        .overrideProvider(CacheService)
        .useValue(mockCacheService)
        .compile();

        app = moduleFixture.createNestApplication();
        await app.init();

        // Clean up database
        const dataSource = moduleFixture.get('DataSource');
        await dataSource.synchronize(true);
    }, 30000);

    afterAll(async () => {
        if (app) {
            await app.close();
        }
    });

    describe('Public News Categories', () => {
        let categoryId: number;

        beforeEach(async () => {
            // Create test category
            const categoryData = {
                slug: 'test-category',
                name: 'Test Category',
                description: 'Test category description',
                icon: 'test',
                color: '#FF0000',
                sortOrder: 1,
                isActive: true,
                isPublic: true,
                createdBy: 1,
            };

            const dataSource = moduleFixture.get('DataSource');
            const categoryRepository = dataSource.getRepository(NewsCategory);
            const savedCategory = await categoryRepository.save(categoryData);
            categoryId = savedCategory.id;
        });

        it('/news/categories (GET) should return public categories', async () => {
            const response = await request(app.getHttpServer())
                .get('/news/categories')
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('slug');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('isActive', true);
            expect(response.body[0]).toHaveProperty('isPublic', true);
        });

        it('/news/categories/:slug (GET) should return category by slug', async () => {
            const response = await request(app.getHttpServer())
                .get('/news/categories/test-category')
                .expect(200);

            expect(response.body).toHaveProperty('id', categoryId);
            expect(response.body).toHaveProperty('slug', 'test-category');
            expect(response.body).toHaveProperty('name', 'Test Category');
            expect(response.body).toHaveProperty('description', 'Test category description');
        });

        it('/news/categories/non-existent (GET) should return 404', async () => {
            await request(app.getHttpServer())
                .get('/news/categories/non-existent')
                .expect(404);
        });
    });

    describe('Public News Articles', () => {
        let categoryId: number;
        let articleId: number;

        beforeEach(async () => {
            const dataSource = moduleFixture.get('DataSource');
            
            // Create test category
            const categoryRepository = dataSource.getRepository(NewsCategory);
            const savedCategory = await categoryRepository.save({
                slug: 'article-test-category',
                name: 'Article Test Category',
                createdBy: 1,
                isActive: true,
                isPublic: true,
            });
            categoryId = savedCategory.id;

            // Create test article
            const articleRepository = dataSource.getRepository(NewsArticle);
            const savedArticle = await articleRepository.save({
                title: 'Test Article',
                slug: 'test-article-2025-05-30',
                excerpt: 'Test article excerpt',
                content: '<p>Test article content</p>',
                status: ArticleStatus.PUBLISHED,
                publishedAt: new Date(),
                categoryId: categoryId,
                authorId: 1,
                isFeatured: true,
                priority: 5,
                viewCount: 0,
                shareCount: 0,
                likeCount: 0,
            });
            articleId = savedArticle.id;
        });

        it('/news (GET) should return published articles', async () => {
            const response = await request(app.getHttpServer())
                .get('/news')
                .expect(200);

            expect(response.body).toHaveProperty('data');
            expect(response.body).toHaveProperty('total');
            expect(response.body).toHaveProperty('page');
            expect(response.body).toHaveProperty('limit');
            expect(Array.isArray(response.body.data)).toBe(true);
            
            if (response.body.data.length > 0) {
                expect(response.body.data[0]).toHaveProperty('id');
                expect(response.body.data[0]).toHaveProperty('title');
                expect(response.body.data[0]).toHaveProperty('slug');
                expect(response.body.data[0]).toHaveProperty('status', ArticleStatus.PUBLISHED);
            }
        });

        it('/news/featured (GET) should return featured articles', async () => {
            const response = await request(app.getHttpServer())
                .get('/news/featured')
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            
            if (response.body.length > 0) {
                expect(response.body[0]).toHaveProperty('id');
                expect(response.body[0]).toHaveProperty('title');
                expect(response.body[0]).toHaveProperty('isFeatured', true);
            }
        });

        it('/news/featured?limit=3 (GET) should return limited featured articles', async () => {
            const response = await request(app.getHttpServer())
                .get('/news/featured?limit=3')
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeLessThanOrEqual(3);
        });

        it('/news/category/:categorySlug (GET) should return articles by category', async () => {
            const response = await request(app.getHttpServer())
                .get('/news/category/article-test-category')
                .expect(200);

            expect(response.body).toHaveProperty('data');
            expect(response.body).toHaveProperty('total');
            expect(Array.isArray(response.body.data)).toBe(true);
        });

        it('/news/:slug (GET) should return article by slug', async () => {
            const response = await request(app.getHttpServer())
                .get('/news/test-article-2025-05-30')
                .expect(200);

            expect(response.body).toHaveProperty('id', articleId);
            expect(response.body).toHaveProperty('slug', 'test-article-2025-05-30');
            expect(response.body).toHaveProperty('title', 'Test Article');
            expect(response.body).toHaveProperty('content', '<p>Test article content</p>');
        });

        it('/news/non-existent-slug (GET) should return 404', async () => {
            await request(app.getHttpServer())
                .get('/news/non-existent-slug')
                .expect(404);
        });

        it('/news/:id/view (POST) should increment view count', async () => {
            const response = await request(app.getHttpServer())
                .post(`/news/${articleId}/view`)
                .expect(204);

            expect(response.body).toEqual({});
        });

        it('/news/:id/share (POST) should increment share count', async () => {
            const response = await request(app.getHttpServer())
                .post(`/news/${articleId}/share`)
                .expect(204);

            expect(response.body).toEqual({});
        });

        it('/news/999/view (POST) should return 404 for non-existent article', async () => {
            await request(app.getHttpServer())
                .post('/news/999/view')
                .expect(404);
        });
    });

    describe('Query Parameters', () => {
        it('/news?page=1&limit=5 (GET) should handle pagination', async () => {
            const response = await request(app.getHttpServer())
                .get('/news?page=1&limit=5')
                .expect(200);

            expect(response.body).toHaveProperty('page', 1);
            expect(response.body).toHaveProperty('limit', 5);
            expect(response.body.data.length).toBeLessThanOrEqual(5);
        });

        it('/news?search=test (GET) should handle search parameter', async () => {
            const response = await request(app.getHttpServer())
                .get('/news?search=test')
                .expect(200);

            expect(response.body).toHaveProperty('data');
            expect(Array.isArray(response.body.data)).toBe(true);
        });

        it('/news?tags=football,transfer (GET) should handle tags filter', async () => {
            const response = await request(app.getHttpServer())
                .get('/news?tags=football,transfer')
                .expect(200);

            expect(response.body).toHaveProperty('data');
            expect(Array.isArray(response.body.data)).toBe(true);
        });
    });

    describe('Error Handling', () => {
        it('should handle invalid query parameters gracefully', async () => {
            const response = await request(app.getHttpServer())
                .get('/news?page=invalid&limit=invalid')
                .expect(200); // Should use defaults

            expect(response.body).toHaveProperty('data');
            expect(response.body).toHaveProperty('page');
            expect(response.body).toHaveProperty('limit');
        });

        it('should handle empty database gracefully', async () => {
            // Clear all data
            const dataSource = moduleFixture.get('DataSource');
            await dataSource.synchronize(true);

            const response = await request(app.getHttpServer())
                .get('/news')
                .expect(200);

            expect(response.body.data).toEqual([]);
            expect(response.body.total).toBe(0);
        });
    });
});
