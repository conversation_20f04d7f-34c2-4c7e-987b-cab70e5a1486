import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { NewsArticleService } from '../../../src/news/articles/services/news-article.service';
import { NewsArticle, ArticleStatus } from '../../../src/news/articles/entities/news-article.entity';
import { NewsCategory } from '../../../src/news/categories/entities/news-category.entity';
import { CacheService } from '../../../src/core/cache/cache.service';
import { CreateArticleDto, UpdateArticleDto, GetArticlesDto } from '../../../src/news/articles/dto';

describe('NewsArticleService', () => {
    let service: NewsArticleService;
    let articleRepository: jest.Mocked<Repository<NewsArticle>>;
    let categoryRepository: jest.Mocked<Repository<NewsCategory>>;
    let cacheService: jest.Mocked<CacheService>;

    const mockCategory: NewsCategory = {
        id: 1,
        slug: 'transfer-news',
        name: 'Transfer News',
        description: 'Latest transfer rumors',
        icon: 'transfer',
        color: '#FF6B35',
        sortOrder: 1,
        isActive: true,
        isPublic: true,
        metaTitle: 'Transfer News',
        metaDescription: 'Latest transfer news',
        articleCount: 5,
        publishedArticleCount: 3,
        createdBy: 1,
        updatedBy: null as any,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    const mockArticle: NewsArticle = {
        id: 1,
        title: 'Messi Signs New Contract',
        slug: 'messi-signs-new-contract-2025-05-30',
        excerpt: 'Lionel Messi has signed a new contract',
        content: '<p>Messi signs new contract...</p>',
        featuredImage: '/uploads/2025/05/30/messi.jpg',
        tags: ['messi', 'barcelona', 'transfer'],
        status: ArticleStatus.PUBLISHED,
        publishedAt: new Date('2025-05-30T10:00:00.000Z'),
        metaTitle: 'Messi Returns to Barcelona',
        metaDescription: 'Lionel Messi signs new contract',
        relatedLeagueId: 140,
        relatedTeamId: 529,
        relatedPlayerId: 154,
        relatedFixtureId: null as any,
        viewCount: 1250,
        shareCount: 45,
        likeCount: 89,
        isFeatured: true,
        priority: 5,
        category: mockCategory,
        categoryId: 1,
        authorId: 1,
        updatedBy: null as any,
        searchVector: null as any,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    beforeEach(async () => {
        const mockArticleRepository = {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn(),
            increment: jest.fn(),
            count: jest.fn(),
        };

        const mockCategoryRepository = {
            findOne: jest.fn(),
            update: jest.fn(),
        };

        const mockCacheService = {
            getCache: jest.fn(),
            setCache: jest.fn(),
            deleteCache: jest.fn(),
            deleteByPattern: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                NewsArticleService,
                {
                    provide: getRepositoryToken(NewsArticle),
                    useValue: mockArticleRepository,
                },
                {
                    provide: getRepositoryToken(NewsCategory),
                    useValue: mockCategoryRepository,
                },
                {
                    provide: CacheService,
                    useValue: mockCacheService,
                },
            ],
        }).compile();

        service = module.get<NewsArticleService>(NewsArticleService);
        articleRepository = module.get(getRepositoryToken(NewsArticle));
        categoryRepository = module.get(getRepositoryToken(NewsCategory));
        cacheService = module.get(CacheService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createArticle', () => {
        it('should create a new article successfully', async () => {
            const createDto: CreateArticleDto = {
                title: 'New Transfer News',
                excerpt: 'Latest transfer update',
                content: '<p>Transfer news content...</p>',
                categoryId: 1,
                tags: ['transfer', 'football'],
                isFeatured: false,
                priority: 1,
            };

            categoryRepository.findOne.mockResolvedValue(mockCategory);
            articleRepository.findOne
                .mockResolvedValueOnce(null) // First call for slug check
                .mockResolvedValueOnce({ ...mockArticle, ...createDto, id: 2 }); // Second call for getArticleWithCategory
            articleRepository.create.mockReturnValue({ ...createDto, authorId: 1 } as NewsArticle);
            articleRepository.save.mockResolvedValue({ ...mockArticle, ...createDto, id: 2 });
            articleRepository.count.mockResolvedValue(5); // Mock count for category update
            categoryRepository.update.mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

            const result = await service.createArticle(createDto, 1);

            expect(categoryRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
            expect(articleRepository.create).toHaveBeenCalled();
            expect(articleRepository.save).toHaveBeenCalled();
            expect(result.title).toBe('New Transfer News');
        });

        it('should throw NotFoundException if category not found', async () => {
            const createDto: CreateArticleDto = {
                title: 'New Article',
                content: 'Content',
                categoryId: 999,
            };

            categoryRepository.findOne.mockResolvedValue(null);

            await expect(service.createArticle(createDto, 1)).rejects.toThrow(BadRequestException);
        });

        it('should throw BadRequestException if slug already exists', async () => {
            const createDto: CreateArticleDto = {
                title: 'Duplicate Title',
                content: 'Content',
                categoryId: 1,
            };

            categoryRepository.findOne.mockResolvedValue(mockCategory);
            articleRepository.findOne.mockResolvedValue(mockArticle); // Existing slug

            await expect(service.createArticle(createDto, 1)).rejects.toThrow(BadRequestException);
        });
    });

    describe('getArticleById', () => {
        it('should return article from cache if available', async () => {
            const cachedArticle = JSON.stringify(mockArticle);
            cacheService.getCache.mockResolvedValue(cachedArticle);

            const result = await service.getArticleById(1);

            expect(cacheService.getCache).toHaveBeenCalledWith('news_article_id_1');
            expect(result.id).toBe(1);
            expect(result.title).toBe('Messi Signs New Contract');
        });

        it('should fetch from database and cache if not in cache', async () => {
            cacheService.getCache.mockResolvedValue(null);
            articleRepository.findOne.mockResolvedValue(mockArticle);

            const result = await service.getArticleById(1);

            expect(articleRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                relations: ['category']
            });
            expect(cacheService.setCache).toHaveBeenCalled();
            expect(result.id).toBe(1);
        });

        it('should throw NotFoundException if article not found', async () => {
            cacheService.getCache.mockResolvedValue(null);
            articleRepository.findOne.mockResolvedValue(null);

            await expect(service.getArticleById(999)).rejects.toThrow(NotFoundException);
        });
    });

    describe('getArticleBySlug', () => {
        it('should return article by slug', async () => {
            cacheService.getCache.mockResolvedValue(null);
            articleRepository.findOne.mockResolvedValue(mockArticle);

            const result = await service.getArticleBySlug('messi-signs-new-contract-2025-05-30');

            expect(articleRepository.findOne).toHaveBeenCalledWith({
                where: { slug: 'messi-signs-new-contract-2025-05-30' },
                relations: ['category']
            });
            expect(result.slug).toBe('messi-signs-new-contract-2025-05-30');
        });

        it('should throw NotFoundException if article not found', async () => {
            cacheService.getCache.mockResolvedValue(null);
            articleRepository.findOne.mockResolvedValue(null);

            await expect(service.getArticleBySlug('non-existent')).rejects.toThrow(NotFoundException);
        });
    });

    describe('updateArticle', () => {
        it('should update article successfully', async () => {
            const updateDto: UpdateArticleDto = {
                title: 'Updated Title',
                content: 'Updated content',
            };

            articleRepository.findOne
                .mockResolvedValueOnce(mockArticle) // First call for finding article
                .mockResolvedValueOnce({ ...mockArticle, ...updateDto }); // Second call for getArticleWithCategory
            articleRepository.save.mockResolvedValue({ ...mockArticle, ...updateDto });
            articleRepository.count.mockResolvedValue(5); // Mock count for category update
            categoryRepository.update.mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

            const result = await service.updateArticle(1, updateDto, 1);

            expect(articleRepository.findOne).toHaveBeenCalledTimes(2);
            expect(articleRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                relations: ['category']
            });
            expect(articleRepository.save).toHaveBeenCalled();
            expect(cacheService.deleteByPattern).toHaveBeenCalledWith('news_article_*');
            expect(result.title).toBe('Updated Title');
        });

        it('should throw NotFoundException if article not found', async () => {
            const updateDto: UpdateArticleDto = { title: 'Updated Title' };
            articleRepository.findOne.mockResolvedValue(null);

            await expect(service.updateArticle(999, updateDto, 1)).rejects.toThrow(NotFoundException);
        });
    });

    describe('deleteArticle', () => {
        it('should delete article successfully', async () => {
            articleRepository.findOne.mockResolvedValue(mockArticle);
            articleRepository.remove.mockResolvedValue(mockArticle);
            articleRepository.count.mockResolvedValue(4); // Mock count for category update
            categoryRepository.update.mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

            await service.deleteArticle(1);

            expect(articleRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
            expect(articleRepository.remove).toHaveBeenCalledWith(mockArticle);
            expect(cacheService.deleteByPattern).toHaveBeenCalledWith('news_article_*');
        });

        it('should throw NotFoundException if article not found', async () => {
            articleRepository.findOne.mockResolvedValue(null);

            await expect(service.deleteArticle(999)).rejects.toThrow(NotFoundException);
        });
    });

    describe('publishArticle', () => {
        it('should publish article successfully', async () => {
            const draftArticle = { ...mockArticle, status: ArticleStatus.DRAFT, publishedAt: undefined };
            articleRepository.findOne.mockResolvedValue(draftArticle);
            articleRepository.save.mockResolvedValue({
                ...draftArticle,
                status: ArticleStatus.PUBLISHED,
                publishedAt: new Date()
            });
            articleRepository.count.mockResolvedValue(5); // Mock count for category update
            categoryRepository.update.mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

            const result = await service.publishArticle(1);

            expect(result.status).toBe(ArticleStatus.PUBLISHED);
            expect(result.publishedAt).toBeDefined();
        });
    });

    describe('incrementViewCount', () => {
        it('should increment view count', async () => {
            articleRepository.increment.mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

            await service.incrementViewCount(1);

            expect(articleRepository.increment).toHaveBeenCalledWith({ id: 1 }, 'viewCount', 1);
            expect(cacheService.deleteByPattern).toHaveBeenCalledWith('news_article_*');
        });
    });

    describe('incrementShareCount', () => {
        it('should increment share count', async () => {
            articleRepository.increment.mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

            await service.incrementShareCount(1);

            expect(articleRepository.increment).toHaveBeenCalledWith({ id: 1 }, 'shareCount', 1);
            expect(cacheService.deleteByPattern).toHaveBeenCalledWith('news_article_*');
        });
    });
});
