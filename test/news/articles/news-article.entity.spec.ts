import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { NewsArticle, ArticleStatus } from '../../../src/news/articles/entities/news-article.entity';
import { NewsCategory } from '../../../src/news/categories/entities/news-category.entity';
import { DatabaseService } from '../../../src/core/database/database.service';
import configuration from '../../../src/core/config/configuration';

describe('NewsArticle Entity', () => {
    let dataSource: DataSource;

    beforeAll(async () => {
        const module: TestingModule = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({ load: [configuration] }),
                TypeOrmModule.forRootAsync({
                    imports: [ConfigModule],
                    useClass: DatabaseService,
                }),
                TypeOrmModule.forFeature([NewsArticle, NewsCategory]),
            ],
        }).compile();

        dataSource = module.get(DataSource);
        await dataSource.synchronize(true); // Reset DB
    }, 15000);

    afterAll(async () => {
        if (dataSource && dataSource.isInitialized) {
            await dataSource.destroy();
        }
    });

    it('should save and retrieve news article', async () => {
        // First create a category
        const categoryRepository = dataSource.getRepository(NewsCategory);
        const category = await categoryRepository.save({
            slug: 'test-category',
            name: 'Test Category',
            createdBy: 1,
        });

        const articleData: Partial<NewsArticle> = {
            title: 'Messi Signs New Contract with Barcelona',
            slug: 'messi-signs-new-contract-barcelona-2025-05-30',
            excerpt: 'Lionel Messi has signed a new 3-year contract with FC Barcelona',
            content: '<p>In a surprising turn of events, Lionel Messi has decided to return to Barcelona...</p>',
            featuredImage: '/uploads/2025/05/30/messi-barcelona.jpg',
            tags: ['messi', 'barcelona', 'transfer'],
            status: ArticleStatus.PUBLISHED,
            publishedAt: new Date('2025-05-30T10:00:00Z'),
            metaTitle: 'Messi Returns to Barcelona - Official Announcement',
            metaDescription: 'Lionel Messi signs new contract with Barcelona after PSG departure',
            relatedLeagueId: 140, // La Liga
            relatedTeamId: 529, // Barcelona
            relatedPlayerId: 154, // Messi
            viewCount: 1250,
            shareCount: 45,
            likeCount: 89,
            isFeatured: true,
            priority: 5,
            categoryId: category.id,
            authorId: 1,
        };

        const repository = dataSource.getRepository(NewsArticle);
        const savedArticle = await repository.save(articleData);
        const retrievedArticle = await repository.findOneBy({ slug: 'messi-signs-new-contract-barcelona-2025-05-30' });

        expect(retrievedArticle).toBeDefined();
        expect(retrievedArticle?.title).toBe('Messi Signs New Contract with Barcelona');
        expect(retrievedArticle?.slug).toBe('messi-signs-new-contract-barcelona-2025-05-30');
        expect(retrievedArticle?.excerpt).toBe('Lionel Messi has signed a new 3-year contract with FC Barcelona');
        expect(retrievedArticle?.content).toContain('In a surprising turn of events');
        expect(retrievedArticle?.featuredImage).toBe('/uploads/2025/05/30/messi-barcelona.jpg');
        expect(retrievedArticle?.tags).toEqual(['messi', 'barcelona', 'transfer']);
        expect(retrievedArticle?.status).toBe(ArticleStatus.PUBLISHED);
        expect(retrievedArticle?.publishedAt?.toISOString()).toBe('2025-05-30T10:00:00.000Z');
        expect(retrievedArticle?.metaTitle).toBe('Messi Returns to Barcelona - Official Announcement');
        expect(retrievedArticle?.metaDescription).toBe('Lionel Messi signs new contract with Barcelona after PSG departure');
        expect(retrievedArticle?.relatedLeagueId).toBe(140);
        expect(retrievedArticle?.relatedTeamId).toBe(529);
        expect(retrievedArticle?.relatedPlayerId).toBe(154);
        expect(retrievedArticle?.viewCount).toBe(1250);
        expect(retrievedArticle?.shareCount).toBe(45);
        expect(retrievedArticle?.likeCount).toBe(89);
        expect(retrievedArticle?.isFeatured).toBe(true);
        expect(retrievedArticle?.priority).toBe(5);
        expect(retrievedArticle?.categoryId).toBe(category.id);
        expect(retrievedArticle?.authorId).toBe(1);
        expect(retrievedArticle?.createdAt.toISOString()).toMatch(/Z$/); // UTC
        expect(retrievedArticle?.updatedAt.toISOString()).toMatch(/Z$/); // UTC
    });

    it('should enforce unique slug constraint', async () => {
        // First create a category
        const categoryRepository = dataSource.getRepository(NewsCategory);
        const category = await categoryRepository.save({
            slug: 'test-category-unique',
            name: 'Test Category',
            createdBy: 1,
        });

        const repository = dataSource.getRepository(NewsArticle);

        const firstArticle: Partial<NewsArticle> = {
            title: 'First Article',
            slug: 'duplicate-slug-test',
            content: 'First article content',
            categoryId: category.id,
            authorId: 1,
        };

        const secondArticle: Partial<NewsArticle> = {
            title: 'Second Article',
            slug: 'duplicate-slug-test', // Same slug
            content: 'Second article content',
            categoryId: category.id,
            authorId: 1,
        };

        await repository.save(firstArticle);

        await expect(repository.save(secondArticle)).rejects.toThrow();
    });

    it('should handle default values correctly', async () => {
        // First create a category
        const categoryRepository = dataSource.getRepository(NewsCategory);
        const category = await categoryRepository.save({
            slug: 'test-category-defaults',
            name: 'Test Category Defaults',
            createdBy: 1,
        });

        const articleData: Partial<NewsArticle> = {
            title: 'Minimal Article',
            slug: 'minimal-article-test',
            content: 'Minimal article content',
            categoryId: category.id,
            authorId: 1,
            // All other fields should use defaults or be nullable
        };

        const repository = dataSource.getRepository(NewsArticle);
        const savedArticle = await repository.save(articleData);
        const retrievedArticle = await repository.findOneBy({ slug: 'minimal-article-test' });

        expect(retrievedArticle).toBeDefined();
        expect(retrievedArticle?.title).toBe('Minimal Article');
        expect(retrievedArticle?.slug).toBe('minimal-article-test');
        expect(retrievedArticle?.content).toBe('Minimal article content');
        expect(retrievedArticle?.excerpt).toBeNull();
        expect(retrievedArticle?.featuredImage).toBeNull();
        expect(retrievedArticle?.tags).toBeNull();
        expect(retrievedArticle?.status).toBe(ArticleStatus.DRAFT); // Default value
        expect(retrievedArticle?.publishedAt).toBeNull();
        expect(retrievedArticle?.metaTitle).toBeNull();
        expect(retrievedArticle?.metaDescription).toBeNull();
        expect(retrievedArticle?.relatedLeagueId).toBeNull();
        expect(retrievedArticle?.relatedTeamId).toBeNull();
        expect(retrievedArticle?.relatedPlayerId).toBeNull();
        expect(retrievedArticle?.relatedFixtureId).toBeNull();
        expect(retrievedArticle?.viewCount).toBe(0); // Default value
        expect(retrievedArticle?.shareCount).toBe(0); // Default value
        expect(retrievedArticle?.likeCount).toBe(0); // Default value
        expect(retrievedArticle?.isFeatured).toBe(false); // Default value
        expect(retrievedArticle?.priority).toBe(0); // Default value
        expect(retrievedArticle?.categoryId).toBe(category.id);
        expect(retrievedArticle?.authorId).toBe(1);
    });

    it('should handle article status transitions', async () => {
        // First create a category
        const categoryRepository = dataSource.getRepository(NewsCategory);
        const category = await categoryRepository.save({
            slug: 'test-category-status',
            name: 'Test Category Status',
            createdBy: 1,
        });

        const repository = dataSource.getRepository(NewsArticle);

        // Create draft article
        const draftArticle: Partial<NewsArticle> = {
            title: 'Status Test Article',
            slug: 'status-test-article',
            content: 'Article content for status testing',
            status: ArticleStatus.DRAFT,
            categoryId: category.id,
            authorId: 1,
        };

        const savedArticle = await repository.save(draftArticle);
        expect(savedArticle.status).toBe(ArticleStatus.DRAFT);
        expect(savedArticle.publishedAt).toBeNull();

        // Publish article
        savedArticle.status = ArticleStatus.PUBLISHED;
        savedArticle.publishedAt = new Date('2025-05-30T12:00:00Z');

        await repository.save(savedArticle);

        const publishedArticle = await repository.findOneBy({ slug: 'status-test-article' });
        expect(publishedArticle?.status).toBe(ArticleStatus.PUBLISHED);
        expect(publishedArticle?.publishedAt?.toISOString()).toBe('2025-05-30T12:00:00.000Z');

        // Archive article
        publishedArticle!.status = ArticleStatus.ARCHIVED;
        await repository.save(publishedArticle!);

        const archivedArticle = await repository.findOneBy({ slug: 'status-test-article' });
        expect(archivedArticle?.status).toBe(ArticleStatus.ARCHIVED);
    });

    it('should handle tags array correctly', async () => {
        // First create a category
        const categoryRepository = dataSource.getRepository(NewsCategory);
        const category = await categoryRepository.save({
            slug: 'test-category-tags',
            name: 'Test Category Tags',
            createdBy: 1,
        });

        const repository = dataSource.getRepository(NewsArticle);

        const articleWithTags: Partial<NewsArticle> = {
            title: 'Tags Test Article',
            slug: 'tags-test-article',
            content: 'Article content with tags',
            tags: ['football', 'premier-league', 'manchester-united', 'transfer'],
            categoryId: category.id,
            authorId: 1,
        };

        await repository.save(articleWithTags);

        const retrievedArticle = await repository.findOneBy({ slug: 'tags-test-article' });

        expect(retrievedArticle?.tags).toEqual(['football', 'premier-league', 'manchester-united', 'transfer']);
        expect(retrievedArticle?.tags).toHaveLength(4);
    });
});
