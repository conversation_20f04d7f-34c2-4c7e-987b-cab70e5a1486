import { Test, TestingModule } from '@nestjs/testing';
import { NewsArticleController } from '../../../src/news/articles/controllers/news-article.controller';
import { NewsArticleService } from '../../../src/news/articles/services/news-article.service';
import { SystemUser } from '../../../src/auth/system/entities/system-user.entity';
import { ArticleStatus } from '../../../src/news/articles/entities/news-article.entity';
import { CreateArticleDto, UpdateArticleDto, GetArticlesDto } from '../../../src/news/articles/dto';

describe('NewsArticleController', () => {
    let controller: NewsArticleController;
    let service: jest.Mocked<NewsArticleService>;

    const mockUser: SystemUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
        fullName: 'Admin User',
        role: 'admin',
        isActive: true,
        lastLoginAt: new Date(),
        createdBy: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        isAdmin: jest.fn(),
        canEditContent: jest.fn(),
        canModerate: jest.fn(),
    };

    const mockArticleResponse = {
        id: 1,
        title: 'Messi Signs New Contract',
        slug: 'messi-signs-new-contract-2025-05-30',
        excerpt: 'Lionel Messi has signed a new contract',
        content: '<p>Messi signs new contract...</p>',
        featuredImage: '/uploads/2025/05/30/messi.jpg',
        tags: ['messi', 'barcelona', 'transfer'],
        status: ArticleStatus.PUBLISHED,
        publishedAt: new Date('2025-05-30T10:00:00.000Z'),
        metaTitle: 'Messi Returns to Barcelona',
        metaDescription: 'Lionel Messi signs new contract',
        relatedLeagueId: 140,
        relatedTeamId: 529,
        relatedPlayerId: 154,
        viewCount: 1250,
        shareCount: 45,
        likeCount: 89,
        isFeatured: true,
        priority: 5,
        category: {
            id: 1,
            slug: 'transfer-news',
            name: 'Transfer News',
            description: 'Latest transfer rumors',
            icon: 'transfer',
            color: '#FF6B35',
            sortOrder: 1,
            isActive: true,
            isPublic: true,
            articleCount: 5,
            publishedArticleCount: 3,
            metaTitle: 'Transfer News',
            metaDescription: 'Latest transfer news',
            createdAt: new Date('2025-05-30T08:00:00.000Z'),
            updatedAt: new Date('2025-05-30T08:00:00.000Z'),
        },
        categoryId: 1,
        authorId: 1,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    const mockPaginatedResponse = {
        data: [mockArticleResponse],
        meta: {
            totalItems: 1,
            totalPages: 1,
            currentPage: 1,
            limit: 10,
        },
        status: 200,
    };

    beforeEach(async () => {
        const mockService = {
            createArticle: jest.fn(),
            getArticles: jest.fn(),
            getArticleById: jest.fn(),
            updateArticle: jest.fn(),
            deleteArticle: jest.fn(),
            publishArticle: jest.fn(),
            unpublishArticle: jest.fn(),
            archiveArticle: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            controllers: [NewsArticleController],
            providers: [
                {
                    provide: NewsArticleService,
                    useValue: mockService,
                },
            ],
        }).compile();

        controller = module.get<NewsArticleController>(NewsArticleController);
        service = module.get(NewsArticleService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('createArticle', () => {
        it('should create a new article', async () => {
            const createDto: CreateArticleDto = {
                title: 'New Transfer News',
                excerpt: 'Latest transfer update',
                content: '<p>Transfer news content...</p>',
                categoryId: 1,
                tags: ['transfer', 'football'],
                isFeatured: false,
                priority: 1,
            };

            service.createArticle.mockResolvedValue(mockArticleResponse);

            const result = await controller.createArticle(createDto, mockUser);

            expect(service.createArticle).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toEqual(mockArticleResponse);
        });
    });

    describe('getArticles', () => {
        it('should return paginated articles', async () => {
            const query: GetArticlesDto = {
                page: 1,
                limit: 10,
                status: ArticleStatus.PUBLISHED,
                categoryId: 1,
                sortBy: 'createdAt',
                sortOrder: 'DESC',
            };

            service.getArticles.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getArticles(query);

            expect(service.getArticles).toHaveBeenCalledWith(query);
            expect(result).toEqual(mockPaginatedResponse);
            expect(result.data).toHaveLength(1);
            expect(result.meta.totalItems).toBe(1);
        });

        it('should handle empty query parameters', async () => {
            const query: GetArticlesDto = {};

            service.getArticles.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getArticles(query);

            expect(service.getArticles).toHaveBeenCalledWith(query);
            expect(result).toEqual(mockPaginatedResponse);
        });
    });

    describe('getArticleById', () => {
        it('should return article by ID', async () => {
            service.getArticleById.mockResolvedValue(mockArticleResponse);

            const result = await controller.getArticleById(1);

            expect(service.getArticleById).toHaveBeenCalledWith(1);
            expect(result).toEqual(mockArticleResponse);
            expect(result.id).toBe(1);
            expect(result.title).toBe('Messi Signs New Contract');
        });
    });

    describe('updateArticle', () => {
        it('should update article successfully', async () => {
            const updateDto: UpdateArticleDto = {
                title: 'Updated Article Title',
                content: 'Updated content',
                tags: ['updated', 'tags'],
            };

            const updatedResponse = {
                ...mockArticleResponse,
                ...updateDto,
                category: mockArticleResponse.category,
            };

            service.updateArticle.mockResolvedValue(updatedResponse);

            const result = await controller.updateArticle(1, updateDto, mockUser);

            expect(service.updateArticle).toHaveBeenCalledWith(1, updateDto, mockUser.id);
            expect(result).toEqual(updatedResponse);
            expect(result.title).toBe('Updated Article Title');
        });
    });

    describe('deleteArticle', () => {
        it('should delete article successfully', async () => {
            service.deleteArticle.mockResolvedValue(undefined);

            await controller.deleteArticle(1);

            expect(service.deleteArticle).toHaveBeenCalledWith(1);
        });
    });

    describe('publishArticle', () => {
        it('should publish article successfully', async () => {
            const publishedResponse = {
                ...mockArticleResponse,
                status: ArticleStatus.PUBLISHED,
                publishedAt: new Date('2025-05-30T12:00:00.000Z'),
                category: mockArticleResponse.category,
            };

            service.publishArticle.mockResolvedValue(publishedResponse);

            const result = await controller.publishArticle(1);

            expect(service.publishArticle).toHaveBeenCalledWith(1);
            expect(result).toEqual(publishedResponse);
            expect(result.status).toBe(ArticleStatus.PUBLISHED);
            expect(result.publishedAt).toBeDefined();
        });
    });

    describe('unpublishArticle', () => {
        it('should unpublish article successfully', async () => {
            const unpublishedResponse = {
                ...mockArticleResponse,
                status: ArticleStatus.DRAFT,
                publishedAt: undefined,
                category: mockArticleResponse.category,
            };

            service.unpublishArticle.mockResolvedValue(unpublishedResponse);

            const result = await controller.unpublishArticle(1);

            expect(service.unpublishArticle).toHaveBeenCalledWith(1);
            expect(result).toEqual(unpublishedResponse);
            expect(result.status).toBe(ArticleStatus.DRAFT);
        });
    });

    describe('archiveArticle', () => {
        it('should archive article successfully', async () => {
            const archivedResponse = {
                ...mockArticleResponse,
                status: ArticleStatus.ARCHIVED,
                category: mockArticleResponse.category,
            };

            service.archiveArticle.mockResolvedValue(archivedResponse);

            const result = await controller.archiveArticle(1);

            expect(service.archiveArticle).toHaveBeenCalledWith(1);
            expect(result).toEqual(archivedResponse);
            expect(result.status).toBe(ArticleStatus.ARCHIVED);
        });
    });

    describe('error handling', () => {
        it('should handle service errors in createArticle', async () => {
            const createDto: CreateArticleDto = {
                title: 'Invalid Article',
                content: 'Content',
                categoryId: 999, // Non-existent category
            };

            service.createArticle.mockRejectedValue(new Error('Category not found'));

            await expect(controller.createArticle(createDto, mockUser)).rejects.toThrow('Category not found');
        });

        it('should handle service errors in getArticleById', async () => {
            service.getArticleById.mockRejectedValue(new Error('Article not found'));

            await expect(controller.getArticleById(999)).rejects.toThrow('Article not found');
        });

        it('should handle service errors in updateArticle', async () => {
            const updateDto: UpdateArticleDto = { title: 'Updated Title' };

            service.updateArticle.mockRejectedValue(new Error('Article not found'));

            await expect(controller.updateArticle(999, updateDto, mockUser)).rejects.toThrow('Article not found');
        });

        it('should handle service errors in deleteArticle', async () => {
            service.deleteArticle.mockRejectedValue(new Error('Article not found'));

            await expect(controller.deleteArticle(999)).rejects.toThrow('Article not found');
        });

        it('should handle service errors in publishArticle', async () => {
            service.publishArticle.mockRejectedValue(new Error('Article not found'));

            await expect(controller.publishArticle(999)).rejects.toThrow('Article not found');
        });
    });

    describe('input validation', () => {
        it('should handle valid article creation data', async () => {
            const createDto: CreateArticleDto = {
                title: 'Valid Article Title',
                excerpt: 'Valid excerpt',
                content: '<p>Valid article content</p>',
                featuredImage: '/uploads/2025/05/30/image.jpg',
                tags: ['football', 'news'],
                categoryId: 1,
                relatedLeagueId: 140,
                relatedTeamId: 529,
                relatedPlayerId: 154,
                isFeatured: true,
                priority: 5,
                metaTitle: 'Valid Meta Title',
                metaDescription: 'Valid meta description',
            };

            service.createArticle.mockResolvedValue(mockArticleResponse);

            const result = await controller.createArticle(createDto, mockUser);

            expect(service.createArticle).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toEqual(mockArticleResponse);
        });

        it('should handle minimal article creation data', async () => {
            const createDto: CreateArticleDto = {
                title: 'Minimal Article',
                content: 'Minimal content',
                categoryId: 1,
            };

            service.createArticle.mockResolvedValue(mockArticleResponse);

            const result = await controller.createArticle(createDto, mockUser);

            expect(service.createArticle).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toEqual(mockArticleResponse);
        });
    });
});
