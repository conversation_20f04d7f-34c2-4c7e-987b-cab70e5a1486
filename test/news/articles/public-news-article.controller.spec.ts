import { Test, TestingModule } from '@nestjs/testing';
import { PublicNewsArticleController } from '../../../src/news/articles/controllers/public-news-article.controller';
import { NewsArticleService } from '../../../src/news/articles/services/news-article.service';
import { ArticleStatus } from '../../../src/news/articles/entities/news-article.entity';
import { PublicArticlesDto } from '../../../src/news/articles/dto';

describe('PublicNewsArticleController', () => {
    let controller: PublicNewsArticleController;
    let service: jest.Mocked<NewsArticleService>;

    const mockArticleResponse = {
        id: 1,
        title: '<PERSON><PERSON> Signs New Contract with Barcelona',
        slug: 'messi-signs-new-contract-barcelona-2025-05-30',
        excerpt: '<PERSON> has signed a new 3-year contract with FC Barcelona',
        content: '<p>In a surprising turn of events, <PERSON> has decided to return to Barcelona...</p>',
        featuredImage: '/uploads/2025/05/30/messi-barcelona.jpg',
        tags: ['messi', 'barcelona', 'transfer'],
        status: ArticleStatus.PUBLISHED,
        publishedAt: new Date('2025-05-30T10:00:00.000Z'),
        metaTitle: 'Messi Returns to Barcelona - Official Announcement',
        metaDescription: 'Lionel Messi signs new contract with Barcelona after PSG departure',
        relatedLeagueId: 140,
        relatedTeamId: 529,
        relatedPlayerId: 154,
        viewCount: 1250,
        shareCount: 45,
        likeCount: 89,
        isFeatured: true,
        priority: 5,
        categoryId: 1,
        category: {
            id: 1,
            slug: 'transfer-news',
            name: 'Transfer News',
            description: 'Latest transfer rumors, confirmations and market updates',
            icon: 'transfer',
            color: '#FF6B35',
            sortOrder: 1,
            isActive: true,
            isPublic: true,
            articleCount: 5,
            publishedArticleCount: 3,
            metaTitle: 'Transfer News',
            metaDescription: 'Latest transfer news',
            createdAt: new Date('2025-05-30T08:00:00.000Z'),
            updatedAt: new Date('2025-05-30T08:00:00.000Z'),
        },
        authorId: 1,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z')
    };

    const mockFeaturedArticle = {
        id: 2,
        title: 'Champions League Final Preview',
        slug: 'champions-league-final-preview-2025',
        excerpt: 'Everything you need to know about the upcoming Champions League final',
        content: '<p>The Champions League final is approaching...</p>',
        featuredImage: '/uploads/2025/05/30/ucl-final.jpg',
        tags: ['champions-league', 'final', 'preview'],
        status: ArticleStatus.PUBLISHED,
        publishedAt: new Date('2025-05-30T09:00:00.000Z'),
        metaTitle: 'Champions League Final 2025 Preview',
        metaDescription: 'Complete preview of the Champions League final',
        relatedLeagueId: 2,
        viewCount: 890,
        shareCount: 32,
        likeCount: 67,
        isFeatured: true,
        priority: 4,
        categoryId: 2,
        category: {
            id: 2,
            slug: 'match-reports',
            name: 'Match Reports',
            description: 'Detailed analysis and reports from recent matches',
            icon: 'match',
            color: '#4ECDC4',
            sortOrder: 2,
            isActive: true,
            isPublic: true,
            articleCount: 18,
            publishedArticleCount: 15,
            metaTitle: 'Match Reports',
            metaDescription: 'Detailed match reports',
            createdAt: new Date('2025-05-30T08:00:00.000Z'),
            updatedAt: new Date('2025-05-30T08:00:00.000Z'),
        },
        authorId: 1,
        createdAt: new Date('2025-05-30T07:00:00.000Z'),
        updatedAt: new Date('2025-05-30T07:00:00.000Z')
    };

    const mockPaginatedResponse = {
        data: [mockArticleResponse],
        meta: {
            totalItems: 1,
            totalPages: 1,
            currentPage: 1,
            limit: 10,
        },
        status: 200,
    };

    beforeEach(async () => {
        const mockService = {
            getPublishedArticles: jest.fn(),
            getFeaturedArticles: jest.fn(),
            getArticlesByCategory: jest.fn(),
            getArticleBySlug: jest.fn(),
            incrementViewCount: jest.fn(),
            incrementShareCount: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            controllers: [PublicNewsArticleController],
            providers: [
                {
                    provide: NewsArticleService,
                    useValue: mockService,
                },
            ],
        }).compile();

        controller = module.get<PublicNewsArticleController>(PublicNewsArticleController);
        service = module.get(NewsArticleService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('getPublishedArticles', () => {
        it('should return paginated published articles', async () => {
            const query: PublicArticlesDto = {
                page: 1,
                limit: 10,
                search: 'messi',
                tags: 'transfer',
            };

            service.getPublishedArticles.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getPublishedArticles(query);

            expect(service.getPublishedArticles).toHaveBeenCalledWith(query);
            expect(result).toEqual(mockPaginatedResponse);
            expect(result.data).toHaveLength(1);
            expect(result.meta.totalItems).toBe(1);
        });

        it('should handle empty query parameters', async () => {
            const query: PublicArticlesDto = {};

            service.getPublishedArticles.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getPublishedArticles(query);

            expect(service.getPublishedArticles).toHaveBeenCalledWith(query);
            expect(result).toEqual(mockPaginatedResponse);
        });

        it('should return empty result when no articles found', async () => {
            const query: PublicArticlesDto = { search: 'nonexistent' };
            const emptyResponse = {
                data: [],
                meta: {
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                    limit: 10,
                },
                status: 200,
            };

            service.getPublishedArticles.mockResolvedValue(emptyResponse);

            const result = await controller.getPublishedArticles(query);

            expect(service.getPublishedArticles).toHaveBeenCalledWith(query);
            expect(result).toEqual(emptyResponse);
            expect(result.data).toHaveLength(0);
        });
    });

    describe('getFeaturedArticles', () => {
        it('should return featured articles with default limit', async () => {
            const featuredArticles = [mockArticleResponse, mockFeaturedArticle];
            service.getFeaturedArticles.mockResolvedValue(featuredArticles);

            const result = await controller.getFeaturedArticles();

            expect(service.getFeaturedArticles).toHaveBeenCalledWith(5); // Default limit
            expect(result).toEqual(featuredArticles);
            expect(result).toHaveLength(2);
        });

        it('should return featured articles with custom limit', async () => {
            const featuredArticles = [mockArticleResponse];
            service.getFeaturedArticles.mockResolvedValue(featuredArticles);

            const result = await controller.getFeaturedArticles('1');

            expect(service.getFeaturedArticles).toHaveBeenCalledWith(1);
            expect(result).toEqual(featuredArticles);
            expect(result).toHaveLength(1);
        });

        it('should return empty array when no featured articles exist', async () => {
            service.getFeaturedArticles.mockResolvedValue([]);

            const result = await controller.getFeaturedArticles();

            expect(service.getFeaturedArticles).toHaveBeenCalledWith(5);
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });
    });

    describe('getArticlesByCategory', () => {
        it('should return articles by category slug', async () => {
            const query: PublicArticlesDto = { page: 1, limit: 10 };

            service.getArticlesByCategory.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getArticlesByCategory('transfer-news', query);

            expect(service.getArticlesByCategory).toHaveBeenCalledWith('transfer-news', query);
            expect(result).toEqual(mockPaginatedResponse);
            expect(result.data).toHaveLength(1);
        });

        it('should handle different category slugs', async () => {
            const query: PublicArticlesDto = { page: 1, limit: 5 };
            const matchReportsResponse = {
                ...mockPaginatedResponse,
                data: [mockFeaturedArticle],
            };

            service.getArticlesByCategory.mockResolvedValue(matchReportsResponse);

            const result = await controller.getArticlesByCategory('match-reports', query);

            expect(service.getArticlesByCategory).toHaveBeenCalledWith('match-reports', query);
            expect(result).toEqual(matchReportsResponse);
        });

        it('should return empty result for non-existent category', async () => {
            const query: PublicArticlesDto = {};
            const emptyResponse = {
                data: [],
                meta: {
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                    limit: 10,
                },
                status: 200,
            };

            service.getArticlesByCategory.mockResolvedValue(emptyResponse);

            const result = await controller.getArticlesByCategory('non-existent-category', query);

            expect(service.getArticlesByCategory).toHaveBeenCalledWith('non-existent-category', query);
            expect(result).toEqual(emptyResponse);
        });
    });

    describe('getArticleBySlug', () => {
        it('should return article by slug', async () => {
            service.getArticleBySlug.mockResolvedValue(mockArticleResponse);

            const result = await controller.getArticleBySlug('messi-signs-new-contract-barcelona-2025-05-30');

            expect(service.getArticleBySlug).toHaveBeenCalledWith('messi-signs-new-contract-barcelona-2025-05-30');
            expect(result).toEqual(mockArticleResponse);
            expect(result.slug).toBe('messi-signs-new-contract-barcelona-2025-05-30');
        });

        it('should handle different article slugs', async () => {
            service.getArticleBySlug.mockResolvedValue(mockFeaturedArticle);

            const result = await controller.getArticleBySlug('champions-league-final-preview-2025');

            expect(service.getArticleBySlug).toHaveBeenCalledWith('champions-league-final-preview-2025');
            expect(result).toEqual(mockFeaturedArticle);
            expect(result.slug).toBe('champions-league-final-preview-2025');
        });

        it('should handle service errors for non-existent article', async () => {
            service.getArticleBySlug.mockRejectedValue(new Error('Article not found'));

            await expect(controller.getArticleBySlug('non-existent-article')).rejects.toThrow('Article not found');
        });
    });

    describe('incrementViewCount', () => {
        it('should increment view count successfully', async () => {
            service.incrementViewCount.mockResolvedValue(undefined);

            await controller.incrementViewCount(1);

            expect(service.incrementViewCount).toHaveBeenCalledWith(1);
        });

        it('should handle service errors', async () => {
            service.incrementViewCount.mockRejectedValue(new Error('Article not found'));

            await expect(controller.incrementViewCount(999)).rejects.toThrow('Article not found');
        });
    });

    describe('incrementShareCount', () => {
        it('should increment share count successfully', async () => {
            service.incrementShareCount.mockResolvedValue(undefined);

            await controller.incrementShareCount(1);

            expect(service.incrementShareCount).toHaveBeenCalledWith(1);
        });

        it('should handle service errors', async () => {
            service.incrementShareCount.mockRejectedValue(new Error('Article not found'));

            await expect(controller.incrementShareCount(999)).rejects.toThrow('Article not found');
        });
    });

    describe('response format validation', () => {
        it('should return articles with all required fields', async () => {
            const query: PublicArticlesDto = {};
            service.getPublishedArticles.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getPublishedArticles(query);

            expect(result.data[0]).toHaveProperty('id');
            expect(result.data[0]).toHaveProperty('title');
            expect(result.data[0]).toHaveProperty('slug');
            expect(result.data[0]).toHaveProperty('excerpt');
            expect(result.data[0]).toHaveProperty('content');
            expect(result.data[0]).toHaveProperty('featuredImage');
            expect(result.data[0]).toHaveProperty('tags');
            expect(result.data[0]).toHaveProperty('status');
            expect(result.data[0]).toHaveProperty('publishedAt');
            expect(result.data[0]).toHaveProperty('viewCount');
            expect(result.data[0]).toHaveProperty('shareCount');
            expect(result.data[0]).toHaveProperty('category');
            expect(result.data[0]).toHaveProperty('createdAt');
            expect(result.data[0]).toHaveProperty('updatedAt');
        });
    });
});
