import { Test, TestingModule } from '@nestjs/testing';
import { PublicNewsCategoryController } from '../../../src/news/categories/controllers/public-news-category.controller';
import { NewsCategoryService } from '../../../src/news/categories/services/news-category.service';

describe('PublicNewsCategoryController', () => {
    let controller: PublicNewsCategoryController;
    let service: jest.Mocked<NewsCategoryService>;

    const mockCategoryResponse = {
        id: 1,
        slug: 'transfer-news',
        name: 'Transfer News',
        description: 'Latest transfer rumors and confirmations',
        icon: 'transfer',
        color: '#FF6B35',
        sortOrder: 1,
        isActive: true,
        isPublic: true,
        metaTitle: 'Transfer News - Football Updates',
        metaDescription: 'Stay updated with the latest football transfer news',
        articleCount: 5,
        publishedArticleCount: 3,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    const mockMatchReportsCategory = {
        id: 2,
        slug: 'match-reports',
        name: 'Match Reports',
        description: 'Detailed analysis and reports from recent matches',
        icon: 'match',
        color: '#4ECDC4',
        sortOrder: 2,
        isActive: true,
        isPublic: true,
        metaTitle: 'Match Reports - Football Analysis',
        metaDescription: 'Read detailed match reports and analysis',
        articleCount: 18,
        publishedArticleCount: 15,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    beforeEach(async () => {
        const mockService = {
            getPublicCategories: jest.fn(),
            getCategoryBySlug: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            controllers: [PublicNewsCategoryController],
            providers: [
                {
                    provide: NewsCategoryService,
                    useValue: mockService,
                },
            ],
        }).compile();

        controller = module.get<PublicNewsCategoryController>(PublicNewsCategoryController);
        service = module.get(NewsCategoryService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('getPublicCategories', () => {
        it('should return all public categories', async () => {
            const publicCategories = [mockCategoryResponse, mockMatchReportsCategory];
            service.getPublicCategories.mockResolvedValue(publicCategories);

            const result = await controller.getPublicCategories();

            expect(service.getPublicCategories).toHaveBeenCalled();
            expect(result).toEqual(publicCategories);
            expect(result).toHaveLength(2);
            expect(result[0].slug).toBe('transfer-news');
            expect(result[1].slug).toBe('match-reports');
        });

        it('should return empty array when no public categories exist', async () => {
            service.getPublicCategories.mockResolvedValue([]);

            const result = await controller.getPublicCategories();

            expect(service.getPublicCategories).toHaveBeenCalled();
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });

        it('should return categories sorted by sortOrder', async () => {
            const unsortedCategories = [
                { ...mockMatchReportsCategory, sortOrder: 2 },
                { ...mockCategoryResponse, sortOrder: 1 },
            ];
            service.getPublicCategories.mockResolvedValue(unsortedCategories);

            const result = await controller.getPublicCategories();

            expect(service.getPublicCategories).toHaveBeenCalled();
            expect(result).toEqual(unsortedCategories);
            expect(result[0].sortOrder).toBe(2);
            expect(result[1].sortOrder).toBe(1);
        });

        it('should only return active and public categories', async () => {
            const publicCategories = [
                mockCategoryResponse,
                mockMatchReportsCategory,
            ];
            service.getPublicCategories.mockResolvedValue(publicCategories);

            const result = await controller.getPublicCategories();

            expect(service.getPublicCategories).toHaveBeenCalled();
            expect(result).toEqual(publicCategories);
            
            // Verify all returned categories are active and public
            result.forEach(category => {
                expect(category.isActive).toBe(true);
                expect(category.isPublic).toBe(true);
            });
        });
    });

    describe('getCategoryBySlug', () => {
        it('should return category by slug', async () => {
            service.getCategoryBySlug.mockResolvedValue(mockCategoryResponse);

            const result = await controller.getCategoryBySlug('transfer-news');

            expect(service.getCategoryBySlug).toHaveBeenCalledWith('transfer-news');
            expect(result).toEqual(mockCategoryResponse);
            expect(result.slug).toBe('transfer-news');
            expect(result.name).toBe('Transfer News');
        });

        it('should handle different category slugs', async () => {
            service.getCategoryBySlug.mockResolvedValue(mockMatchReportsCategory);

            const result = await controller.getCategoryBySlug('match-reports');

            expect(service.getCategoryBySlug).toHaveBeenCalledWith('match-reports');
            expect(result).toEqual(mockMatchReportsCategory);
            expect(result.slug).toBe('match-reports');
            expect(result.name).toBe('Match Reports');
        });

        it('should handle service errors for non-existent category', async () => {
            service.getCategoryBySlug.mockRejectedValue(new Error('Category not found'));

            await expect(controller.getCategoryBySlug('non-existent-category')).rejects.toThrow('Category not found');
            expect(service.getCategoryBySlug).toHaveBeenCalledWith('non-existent-category');
        });

        it('should handle special characters in slug', async () => {
            const specialSlug = 'transfer-news-2025';
            service.getCategoryBySlug.mockResolvedValue(mockCategoryResponse);

            const result = await controller.getCategoryBySlug(specialSlug);

            expect(service.getCategoryBySlug).toHaveBeenCalledWith(specialSlug);
            expect(result).toEqual(mockCategoryResponse);
        });
    });

    describe('error handling', () => {
        it('should handle service errors in getPublicCategories', async () => {
            service.getPublicCategories.mockRejectedValue(new Error('Database connection error'));

            await expect(controller.getPublicCategories()).rejects.toThrow('Database connection error');
        });

        it('should handle service errors in getCategoryBySlug', async () => {
            service.getCategoryBySlug.mockRejectedValue(new Error('Category not found'));

            await expect(controller.getCategoryBySlug('invalid-slug')).rejects.toThrow('Category not found');
        });
    });

    describe('response format validation', () => {
        it('should return categories with all required fields', async () => {
            const publicCategories = [mockCategoryResponse];
            service.getPublicCategories.mockResolvedValue(publicCategories);

            const result = await controller.getPublicCategories();

            expect(result[0]).toHaveProperty('id');
            expect(result[0]).toHaveProperty('slug');
            expect(result[0]).toHaveProperty('name');
            expect(result[0]).toHaveProperty('description');
            expect(result[0]).toHaveProperty('icon');
            expect(result[0]).toHaveProperty('color');
            expect(result[0]).toHaveProperty('sortOrder');
            expect(result[0]).toHaveProperty('isActive');
            expect(result[0]).toHaveProperty('isPublic');
            expect(result[0]).toHaveProperty('metaTitle');
            expect(result[0]).toHaveProperty('metaDescription');
            expect(result[0]).toHaveProperty('articleCount');
            expect(result[0]).toHaveProperty('publishedArticleCount');
            expect(result[0]).toHaveProperty('createdAt');
            expect(result[0]).toHaveProperty('updatedAt');
        });

        it('should return category by slug with all required fields', async () => {
            service.getCategoryBySlug.mockResolvedValue(mockCategoryResponse);

            const result = await controller.getCategoryBySlug('transfer-news');

            expect(result).toHaveProperty('id');
            expect(result).toHaveProperty('slug');
            expect(result).toHaveProperty('name');
            expect(result).toHaveProperty('description');
            expect(result).toHaveProperty('icon');
            expect(result).toHaveProperty('color');
            expect(result).toHaveProperty('sortOrder');
            expect(result).toHaveProperty('isActive');
            expect(result).toHaveProperty('isPublic');
            expect(result).toHaveProperty('metaTitle');
            expect(result).toHaveProperty('metaDescription');
            expect(result).toHaveProperty('articleCount');
            expect(result).toHaveProperty('publishedArticleCount');
            expect(result).toHaveProperty('createdAt');
            expect(result).toHaveProperty('updatedAt');
        });
    });

    describe('caching behavior', () => {
        it('should call service method for getPublicCategories (caching handled by service)', async () => {
            const publicCategories = [mockCategoryResponse];
            service.getPublicCategories.mockResolvedValue(publicCategories);

            await controller.getPublicCategories();
            await controller.getPublicCategories(); // Second call

            expect(service.getPublicCategories).toHaveBeenCalledTimes(2);
        });

        it('should call service method for getCategoryBySlug (caching handled by service)', async () => {
            service.getCategoryBySlug.mockResolvedValue(mockCategoryResponse);

            await controller.getCategoryBySlug('transfer-news');
            await controller.getCategoryBySlug('transfer-news'); // Second call

            expect(service.getCategoryBySlug).toHaveBeenCalledTimes(2);
            expect(service.getCategoryBySlug).toHaveBeenCalledWith('transfer-news');
        });
    });
});
