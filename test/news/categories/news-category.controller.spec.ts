import { Test, TestingModule } from '@nestjs/testing';
import { NewsCategoryController } from '../../../src/news/categories/controllers/news-category.controller';
import { NewsCategoryService } from '../../../src/news/categories/services/news-category.service';
import { SystemUser } from '../../../src/auth/system/entities/system-user.entity';
import { CreateCategoryDto, UpdateCategoryDto, GetCategoriesDto, CategoryOrderDto } from '../../../src/news/categories/dto';

describe('NewsCategoryController', () => {
    let controller: NewsCategoryController;
    let service: jest.Mocked<NewsCategoryService>;

    const mockUser: SystemUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
        fullName: 'Admin User',
        role: 'admin',
        isActive: true,
        lastLoginAt: new Date(),
        createdBy: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        isAdmin: jest.fn(),
        canEditContent: jest.fn(),
        canModerate: jest.fn(),
    };

    const mockCategoryResponse = {
        id: 1,
        slug: 'transfer-news',
        name: 'Transfer News',
        description: 'Latest transfer rumors',
        icon: 'transfer',
        color: '#FF6B35',
        sortOrder: 1,
        isActive: true,
        isPublic: true,
        metaTitle: 'Transfer News',
        metaDescription: 'Latest transfer news',
        articleCount: 5,
        publishedArticleCount: 3,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    const mockPaginatedResponse = {
        data: [mockCategoryResponse],
        meta: {
            totalItems: 1,
            totalPages: 1,
            currentPage: 1,
            limit: 10,
        },
        status: 200,
    };

    beforeEach(async () => {
        const mockService = {
            createCategory: jest.fn(),
            getCategories: jest.fn(),
            getCategoryById: jest.fn(),
            updateCategory: jest.fn(),
            deleteCategory: jest.fn(),
            reorderCategories: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            controllers: [NewsCategoryController],
            providers: [
                {
                    provide: NewsCategoryService,
                    useValue: mockService,
                },
            ],
        }).compile();

        controller = module.get<NewsCategoryController>(NewsCategoryController);
        service = module.get(NewsCategoryService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('createCategory', () => {
        it('should create a new category', async () => {
            const createDto: CreateCategoryDto = {
                slug: 'match-reports',
                name: 'Match Reports',
                description: 'Detailed match analysis',
                icon: 'match',
                color: '#4ECDC4',
                sortOrder: 2,
                isActive: true,
                isPublic: true,
                metaTitle: 'Match Reports',
                metaDescription: 'Read detailed match reports',
            };

            service.createCategory.mockResolvedValue(mockCategoryResponse);

            const result = await controller.createCategory(createDto, mockUser);

            expect(service.createCategory).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toEqual(mockCategoryResponse);
        });
    });

    describe('getCategories', () => {
        it('should return paginated categories', async () => {
            const query: GetCategoriesDto = {
                page: 1,
                limit: 10,
                isActive: true,
                sortBy: 'sortOrder',
                sortOrder: 'ASC',
            };

            service.getCategories.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getCategories(query);

            expect(service.getCategories).toHaveBeenCalledWith(query);
            expect(result).toEqual(mockPaginatedResponse);
            expect(result.data).toHaveLength(1);
            expect(result.meta.totalItems).toBe(1);
        });

        it('should handle empty query parameters', async () => {
            const query: GetCategoriesDto = {};

            service.getCategories.mockResolvedValue(mockPaginatedResponse);

            const result = await controller.getCategories(query);

            expect(service.getCategories).toHaveBeenCalledWith(query);
            expect(result).toEqual(mockPaginatedResponse);
        });
    });

    describe('getCategoryById', () => {
        it('should return category by ID', async () => {
            service.getCategoryById.mockResolvedValue(mockCategoryResponse);

            const result = await controller.getCategoryById(1);

            expect(service.getCategoryById).toHaveBeenCalledWith(1);
            expect(result).toEqual(mockCategoryResponse);
            expect(result.id).toBe(1);
            expect(result.slug).toBe('transfer-news');
        });
    });

    describe('updateCategory', () => {
        it('should update category successfully', async () => {
            const updateDto: UpdateCategoryDto = {
                name: 'Updated Transfer News',
                description: 'Updated description',
                color: '#FF0000',
            };

            const updatedResponse = {
                ...mockCategoryResponse,
                ...updateDto,
            };

            service.updateCategory.mockResolvedValue(updatedResponse);

            const result = await controller.updateCategory(1, updateDto, mockUser);

            expect(service.updateCategory).toHaveBeenCalledWith(1, updateDto, mockUser.id);
            expect(result).toEqual(updatedResponse);
            expect(result.name).toBe('Updated Transfer News');
        });
    });

    describe('deleteCategory', () => {
        it('should delete category successfully', async () => {
            service.deleteCategory.mockResolvedValue(undefined);

            await controller.deleteCategory(1);

            expect(service.deleteCategory).toHaveBeenCalledWith(1);
        });
    });

    describe('reorderCategories', () => {
        it('should reorder categories successfully', async () => {
            const orders: CategoryOrderDto[] = [
                { id: 1, sortOrder: 2 },
                { id: 2, sortOrder: 1 },
            ];

            service.reorderCategories.mockResolvedValue(undefined);

            const result = await controller.reorderCategories(orders);

            expect(service.reorderCategories).toHaveBeenCalledWith(orders);
            expect(result).toEqual({ message: 'Categories reordered successfully' });
        });
    });

    describe('error handling', () => {
        it('should handle service errors in createCategory', async () => {
            const createDto: CreateCategoryDto = {
                slug: 'duplicate-slug',
                name: 'Duplicate Category',
            };

            service.createCategory.mockRejectedValue(new Error('Slug already exists'));

            await expect(controller.createCategory(createDto, mockUser)).rejects.toThrow('Slug already exists');
        });

        it('should handle service errors in getCategoryById', async () => {
            service.getCategoryById.mockRejectedValue(new Error('Category not found'));

            await expect(controller.getCategoryById(999)).rejects.toThrow('Category not found');
        });

        it('should handle service errors in updateCategory', async () => {
            const updateDto: UpdateCategoryDto = { name: 'Updated Name' };

            service.updateCategory.mockRejectedValue(new Error('Category not found'));

            await expect(controller.updateCategory(999, updateDto, mockUser)).rejects.toThrow('Category not found');
        });

        it('should handle service errors in deleteCategory', async () => {
            service.deleteCategory.mockRejectedValue(new Error('Category not found'));

            await expect(controller.deleteCategory(999)).rejects.toThrow('Category not found');
        });
    });

    describe('input validation', () => {
        it('should handle valid category creation data', async () => {
            const createDto: CreateCategoryDto = {
                slug: 'valid-slug',
                name: 'Valid Category Name',
                description: 'A valid category description',
                icon: 'valid-icon',
                color: '#FFFFFF',
                sortOrder: 1,
                isActive: true,
                isPublic: true,
                metaTitle: 'Valid Meta Title',
                metaDescription: 'Valid meta description',
            };

            service.createCategory.mockResolvedValue(mockCategoryResponse);

            const result = await controller.createCategory(createDto, mockUser);

            expect(service.createCategory).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toEqual(mockCategoryResponse);
        });

        it('should handle minimal category creation data', async () => {
            const createDto: CreateCategoryDto = {
                slug: 'minimal-category',
                name: 'Minimal Category',
            };

            service.createCategory.mockResolvedValue(mockCategoryResponse);

            const result = await controller.createCategory(createDto, mockUser);

            expect(service.createCategory).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toEqual(mockCategoryResponse);
        });
    });
});
