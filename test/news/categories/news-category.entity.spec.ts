import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { NewsCategory } from '../../../src/news/categories/entities/news-category.entity';
import { DatabaseService } from '../../../src/core/database/database.service';
import configuration from '../../../src/core/config/configuration';

describe('NewsCategory Entity', () => {
    let dataSource: DataSource;

    beforeAll(async () => {
        const module: TestingModule = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({ load: [configuration] }),
                TypeOrmModule.forRootAsync({
                    imports: [ConfigModule],
                    useClass: DatabaseService,
                }),
                TypeOrmModule.forFeature([NewsCategory]),
            ],
        }).compile();

        dataSource = module.get(DataSource);
        await dataSource.synchronize(true); // Reset DB
    }, 15000);

    afterAll(async () => {
        if (dataSource && dataSource.isInitialized) {
            await dataSource.destroy();
        }
    });

    it('should save and retrieve news category', async () => {
        const categoryData: Partial<NewsCategory> = {
            slug: 'transfer-news',
            name: 'Transfer News',
            description: 'Latest transfer rumors and confirmations',
            icon: 'transfer',
            color: '#FF6B35',
            sortOrder: 1,
            isActive: true,
            isPublic: true,
            metaTitle: 'Transfer News - Football Updates',
            metaDescription: 'Stay updated with the latest football transfer news',
            articleCount: 0,
            publishedArticleCount: 0,
            createdBy: 1,
        };

        const repository = dataSource.getRepository(NewsCategory);
        const savedCategory = await repository.save(categoryData);
        const retrievedCategory = await repository.findOneBy({ slug: 'transfer-news' });

        expect(retrievedCategory).toBeDefined();
        expect(retrievedCategory?.slug).toBe('transfer-news');
        expect(retrievedCategory?.name).toBe('Transfer News');
        expect(retrievedCategory?.description).toBe('Latest transfer rumors and confirmations');
        expect(retrievedCategory?.icon).toBe('transfer');
        expect(retrievedCategory?.color).toBe('#FF6B35');
        expect(retrievedCategory?.sortOrder).toBe(1);
        expect(retrievedCategory?.isActive).toBe(true);
        expect(retrievedCategory?.isPublic).toBe(true);
        expect(retrievedCategory?.metaTitle).toBe('Transfer News - Football Updates');
        expect(retrievedCategory?.metaDescription).toBe('Stay updated with the latest football transfer news');
        expect(retrievedCategory?.articleCount).toBe(0);
        expect(retrievedCategory?.publishedArticleCount).toBe(0);
        expect(retrievedCategory?.createdBy).toBe(1);
        expect(retrievedCategory?.createdAt.toISOString()).toMatch(/Z$/); // UTC
        expect(retrievedCategory?.updatedAt.toISOString()).toMatch(/Z$/); // UTC
    });

    it('should enforce unique slug constraint', async () => {
        const repository = dataSource.getRepository(NewsCategory);

        const firstCategory: Partial<NewsCategory> = {
            slug: 'match-reports',
            name: 'Match Reports',
            description: 'Detailed match analysis',
            createdBy: 1,
        };

        const secondCategory: Partial<NewsCategory> = {
            slug: 'match-reports', // Same slug
            name: 'Match Reports 2',
            description: 'Another match reports category',
            createdBy: 1,
        };

        await repository.save(firstCategory);

        await expect(repository.save(secondCategory)).rejects.toThrow();
    });

    it('should handle nullable fields correctly', async () => {
        const categoryData: Partial<NewsCategory> = {
            slug: 'minimal-category',
            name: 'Minimal Category',
            createdBy: 1,
            // All other fields are nullable/have defaults
        };

        const repository = dataSource.getRepository(NewsCategory);
        const savedCategory = await repository.save(categoryData);
        const retrievedCategory = await repository.findOneBy({ slug: 'minimal-category' });

        expect(retrievedCategory).toBeDefined();
        expect(retrievedCategory?.slug).toBe('minimal-category');
        expect(retrievedCategory?.name).toBe('Minimal Category');
        expect(retrievedCategory?.description).toBeNull();
        expect(retrievedCategory?.icon).toBeNull();
        expect(retrievedCategory?.color).toBeNull();
        expect(retrievedCategory?.sortOrder).toBe(0); // Default value
        expect(retrievedCategory?.isActive).toBe(true); // Default value
        expect(retrievedCategory?.isPublic).toBe(true); // Default value
        expect(retrievedCategory?.metaTitle).toBeNull();
        expect(retrievedCategory?.metaDescription).toBeNull();
        expect(retrievedCategory?.articleCount).toBe(0); // Default value
        expect(retrievedCategory?.publishedArticleCount).toBe(0); // Default value
        expect(retrievedCategory?.createdBy).toBe(1);
        expect(retrievedCategory?.updatedBy).toBeNull();
    });

    it('should update category correctly', async () => {
        const repository = dataSource.getRepository(NewsCategory);

        // Create initial category
        const initialData: Partial<NewsCategory> = {
            slug: 'update-test',
            name: 'Update Test',
            description: 'Initial description',
            createdBy: 1,
        };

        const savedCategory = await repository.save(initialData);

        // Update category
        savedCategory.name = 'Updated Test';
        savedCategory.description = 'Updated description';
        savedCategory.updatedBy = 2;

        await repository.save(savedCategory);

        const updatedCategory = await repository.findOneBy({ slug: 'update-test' });

        expect(updatedCategory?.name).toBe('Updated Test');
        expect(updatedCategory?.description).toBe('Updated description');
        expect(updatedCategory?.updatedBy).toBe(2);
        expect(updatedCategory?.updatedAt.getTime()).toBeGreaterThan(updatedCategory?.createdAt.getTime() || 0);
    });
});
