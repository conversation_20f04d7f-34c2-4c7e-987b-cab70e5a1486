import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { NewsCategoryService } from '../../../src/news/categories/services/news-category.service';
import { NewsCategory } from '../../../src/news/categories/entities/news-category.entity';
import { CacheService } from '../../../src/core/cache/cache.service';
import { CreateCategoryDto, UpdateCategoryDto, GetCategoriesDto } from '../../../src/news/categories/dto';

describe('NewsCategoryService', () => {
    let service: NewsCategoryService;
    let repository: jest.Mocked<Repository<NewsCategory>>;
    let cacheService: jest.Mocked<CacheService>;

    const mockCategory: NewsCategory = {
        id: 1,
        slug: 'transfer-news',
        name: 'Transfer News',
        description: 'Latest transfer rumors and confirmations',
        icon: 'transfer',
        color: '#FF6B35',
        sortOrder: 1,
        isActive: true,
        isPublic: true,
        metaTitle: 'Transfer News - Football Updates',
        metaDescription: 'Stay updated with the latest football transfer news',
        articleCount: 5,
        publishedArticleCount: 3,
        createdBy: 1,
        updatedBy: null as any,
        createdAt: new Date('2025-05-30T08:00:00.000Z'),
        updatedAt: new Date('2025-05-30T08:00:00.000Z'),
    };

    beforeEach(async () => {
        const mockRepository = {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn(),
        };

        const mockCacheService = {
            getCache: jest.fn(),
            setCache: jest.fn(),
            deleteCache: jest.fn(),
            deleteByPattern: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                NewsCategoryService,
                {
                    provide: getRepositoryToken(NewsCategory),
                    useValue: mockRepository,
                },
                {
                    provide: CacheService,
                    useValue: mockCacheService,
                },
            ],
        }).compile();

        service = module.get<NewsCategoryService>(NewsCategoryService);
        repository = module.get(getRepositoryToken(NewsCategory));
        cacheService = module.get(CacheService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createCategory', () => {
        it('should create a new category successfully', async () => {
            const createDto: CreateCategoryDto = {
                slug: 'match-reports',
                name: 'Match Reports',
                description: 'Detailed match analysis',
                icon: 'match',
                color: '#4ECDC4',
                sortOrder: 2,
                isActive: true,
                isPublic: true,
                metaTitle: 'Match Reports - Football Analysis',
                metaDescription: 'Read detailed match reports and analysis',
            };

            repository.findOne.mockResolvedValue(null); // No existing category
            repository.create.mockReturnValue({ ...createDto, createdBy: 1 } as NewsCategory);
            repository.save.mockResolvedValue({ ...mockCategory, ...createDto, id: 2 });

            const result = await service.createCategory(createDto, 1);

            expect(repository.findOne).toHaveBeenCalledWith({ where: { slug: 'match-reports' } });
            expect(repository.create).toHaveBeenCalledWith({ ...createDto, createdBy: 1 });
            expect(repository.save).toHaveBeenCalled();
            expect(result.slug).toBe('match-reports');
            expect(result.name).toBe('Match Reports');
        });

        it('should throw ConflictException if slug already exists', async () => {
            const createDto: CreateCategoryDto = {
                slug: 'transfer-news',
                name: 'Transfer News',
            };

            repository.findOne.mockResolvedValue(mockCategory);

            await expect(service.createCategory(createDto, 1)).rejects.toThrow(ConflictException);
            expect(repository.findOne).toHaveBeenCalledWith({ where: { slug: 'transfer-news' } });
        });
    });

    describe('getCategoryById', () => {
        it('should return category from cache if available', async () => {
            const cachedCategory = JSON.stringify(mockCategory);
            cacheService.getCache.mockResolvedValue(cachedCategory);

            const result = await service.getCategoryById(1);

            expect(cacheService.getCache).toHaveBeenCalledWith('news_category_id_1');
            expect(result.id).toBe(1);
            expect(result.slug).toBe('transfer-news');
        });

        it('should fetch from database and cache if not in cache', async () => {
            cacheService.getCache.mockResolvedValue(null);
            repository.findOne.mockResolvedValue(mockCategory);

            const result = await service.getCategoryById(1);

            expect(cacheService.getCache).toHaveBeenCalledWith('news_category_id_1');
            expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
            expect(cacheService.setCache).toHaveBeenCalledWith(
                'news_category_id_1',
                expect.any(String),
                3600
            );
            expect(result.id).toBe(1);
        });

        it('should throw NotFoundException if category not found', async () => {
            cacheService.getCache.mockResolvedValue(null);
            repository.findOne.mockResolvedValue(null);

            await expect(service.getCategoryById(999)).rejects.toThrow(NotFoundException);
        });
    });

    describe('getCategoryBySlug', () => {
        it('should return category by slug', async () => {
            cacheService.getCache.mockResolvedValue(null);
            repository.findOne.mockResolvedValue(mockCategory);

            const result = await service.getCategoryBySlug('transfer-news');

            expect(repository.findOne).toHaveBeenCalledWith({ where: { slug: 'transfer-news' } });
            expect(result.slug).toBe('transfer-news');
        });

        it('should throw NotFoundException if category not found', async () => {
            cacheService.getCache.mockResolvedValue(null);
            repository.findOne.mockResolvedValue(null);

            await expect(service.getCategoryBySlug('non-existent')).rejects.toThrow(NotFoundException);
        });
    });

    describe('updateCategory', () => {
        it('should update category successfully', async () => {
            const updateDto: UpdateCategoryDto = {
                name: 'Updated Transfer News',
                description: 'Updated description',
            };

            repository.findOne.mockResolvedValue(mockCategory);
            repository.save.mockResolvedValue({ ...mockCategory, ...updateDto, updatedBy: 1 });

            const result = await service.updateCategory(1, updateDto, 1);

            expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
            expect(repository.save).toHaveBeenCalled();
            expect(cacheService.deleteByPattern).toHaveBeenCalledWith('news_category_*');
            expect(result.name).toBe('Updated Transfer News');
        });

        it('should throw NotFoundException if category not found', async () => {
            const updateDto: UpdateCategoryDto = { name: 'Updated Name' };
            repository.findOne.mockResolvedValue(null);

            await expect(service.updateCategory(999, updateDto, 1)).rejects.toThrow(NotFoundException);
        });
    });

    describe('deleteCategory', () => {
        it('should delete category successfully', async () => {
            repository.findOne.mockResolvedValue(mockCategory);
            repository.remove.mockResolvedValue(mockCategory);

            await service.deleteCategory(1);

            expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
            expect(repository.remove).toHaveBeenCalledWith(mockCategory);
            expect(cacheService.deleteByPattern).toHaveBeenCalledWith('news_category_*');
        });

        it('should throw NotFoundException if category not found', async () => {
            repository.findOne.mockResolvedValue(null);

            await expect(service.deleteCategory(999)).rejects.toThrow(NotFoundException);
        });
    });

    describe('getPublicCategories', () => {
        it('should return public categories from cache', async () => {
            const publicCategories = [mockCategory];
            cacheService.getCache.mockResolvedValue(JSON.stringify(publicCategories));

            const result = await service.getPublicCategories();

            expect(cacheService.getCache).toHaveBeenCalledWith('news_category_public');
            expect(result).toHaveLength(1);
            expect(result[0].slug).toBe('transfer-news');
        });

        it('should fetch public categories from database if not cached', async () => {
            const publicCategories = [mockCategory];
            cacheService.getCache.mockResolvedValue(null);
            repository.find.mockResolvedValue(publicCategories);

            const result = await service.getPublicCategories();

            expect(repository.find).toHaveBeenCalledWith({
                where: { isActive: true, isPublic: true },
                order: { sortOrder: 'ASC' },
            });
            expect(cacheService.setCache).toHaveBeenCalled();
            expect(result).toHaveLength(1);
        });
    });

    describe('getCategories', () => {
        it('should return paginated categories with filters', async () => {
            const query: GetCategoriesDto = {
                page: 1,
                limit: 10,
                isActive: true,
                sortBy: 'sortOrder',
                sortOrder: 'ASC',
            };

            const mockQueryBuilder = {
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([[mockCategory], 1]),
            };

            cacheService.getCache.mockResolvedValue(null);
            repository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

            const result = await service.getCategories(query);

            expect(repository.createQueryBuilder).toHaveBeenCalledWith('category');
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('category.isActive = :isActive', { isActive: true });
            expect(result.data).toHaveLength(1);
            expect(result.meta.totalItems).toBe(1);
            expect(result.meta.currentPage).toBe(1);
            expect(result.meta.limit).toBe(10);
        });
    });
});
