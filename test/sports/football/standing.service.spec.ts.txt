import { Test, TestingModule } from '@nestjs/testing';
import { StandingService } from '../../../src/sports/football/services/standing.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Standing } from '../../../src/sports/football/models/standing.entity';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../../../src/core/cache/cache.service';
import { ImageService } from '../../../src/shared/services/image.service';
import { Repository } from 'typeorm';

describe('StandingService', () => {
      let service: StandingService;
      let standingRepository: jest.Mocked<Repository<Standing>>;
      let cacheService: jest.Mocked<CacheService>;
      let imageService: jest.Mocked<ImageService>;

      beforeEach(async () => {
            const module: TestingModule = await Test.createTestingModule({
                  providers: [
                        StandingService,
                        {
                              provide: getRepositoryToken(Standing),
                              useValue: {
                                    createQueryBuilder: jest.fn(),
                                    findOneBy: jest.fn(),
                                    save: jest.fn(),
                                    upsert: jest.fn(),
                              },
                        },
                        {
                              provide: ConfigService,
                              useValue: {
                                    get: jest.fn((key: string) => {
                                          switch (key) {
                                                case 'app.apiFootballUrl':
                                                      return 'https://v3.football.api-sports.io';
                                                case 'app.apiFootballKey':
                                                      return 'test-api-key';
                                                default:
                                                      return undefined;
                                          }
                                    }),
                              },
                        },
                        {
                              provide: CacheService,
                              useValue: {
                                    getCache: jest.fn(),
                                    setCache: jest.fn(),
                                    deleteByPattern: jest.fn(),
                              },
                        },
                        {
                              provide: ImageService,
                              useValue: {
                                    downloadImage: jest.fn(),
                              },
                        },
                  ],
            }).compile();

            service = module.get<StandingService>(StandingService);
            standingRepository = module.get(getRepositoryToken(Standing));
            cacheService = module.get(CacheService);
            imageService = module.get(ImageService);
      });

      it('should be defined', () => {
            expect(service).toBeDefined();
      });

      it('should return standings from cache', async () => {
            const cachedStandings = {
                  data: [
                        {
                              id: 1,
                              leagueId: 39,
                              leagueName: 'Premier League',
                              season: 2024,
                              teamId: 33,
                              teamName: 'Manchester United',
                              teamLogo: '/images/teams/33.png',
                              position: 1,
                              points: 75,
                              played: 30,
                              win: 22,
                              draw: 9,
                              lose: 1,
                              goalsFor: 65,
                              goalsAgainst: 20,
                              goalsDiff: 45,
                              form: 'WWWDW',
                        },
                  ],
                  meta: {
                        totalItems: 1,
                        totalPages: 1,
                        currentPage: 1,
                        limit: 20,
                  },
                  status: 200,
            };

            jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(JSON.stringify(cachedStandings));

            const result = await service.getStandings({ league: 39, season: 2024 });

            expect(cacheService.getCache).toHaveBeenCalledWith('standings_list_39_2024__1_20');
            expect(result).toEqual(cachedStandings);
      });

      it('should return standings from DB with filters and pagination', async () => {
            const mockStandings = [
                  {
                        id: 1,
                        leagueId: 39,
                        season: 2024,
                        teamId: 33,
                        teamName: 'Manchester United',
                        teamLogo: '/images/teams/33.png',
                        position: 1,
                        points: 75,
                        played: 30,
                        win: 22,
                        draw: 9,
                        lose: 1,
                        goalsFor: 65,
                        goalsAgainst: 20,
                        goalsDiff: 45,
                        form: 'WWWDW',
                        leagueName: 'Premier League',
                        status: '',
                        description: '',
                        groupName: null,
                        source: 'api' as const,
                        createdBy: null,
                        data: {
                              name: 'Regular Season',
                              standings: [{
                                    position: 1,
                                    points: 75,
                                    played: 30,
                                    win: 22,
                                    draw: 9,
                                    lose: 1,
                                    goals: { for: 65, against: 20 },
                                    goalsDiff: 45,
                                    form: 'WWWDW',
                                    status: '',
                                    description: '',
                                    update: '2024-05-28T00:00:00Z',
                              }]
                        },
                        createdAt: new Date('2025-05-14T10:00:00.000Z'),
                        updatedAt: new Date('2025-05-14T10:00:00.000Z'),
                  },
            ];

            jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);

            const qb = {
                  andWhere: jest.fn().mockReturnThis(),
                  orderBy: jest.fn().mockReturnThis(),
                  skip: jest.fn().mockReturnThis(),
                  take: jest.fn().mockReturnThis(),
                  getManyAndCount: jest.fn().mockResolvedValueOnce([mockStandings, 1]),
            };
            jest.spyOn(standingRepository, 'createQueryBuilder').mockReturnValueOnce(qb as any);

            const result = await service.getStandings({ league: 39, season: 2024, page: 1, limit: 20, format: 'internal' });

            expect(standingRepository.createQueryBuilder).toHaveBeenCalledWith('standing');
            expect(qb.andWhere).toHaveBeenCalledWith('standing.leagueId = :leagueId', { leagueId: 39 });
            expect(qb.andWhere).toHaveBeenCalledWith('standing.season = :season', { season: 2024 });
            expect((result as any).data).toHaveLength(1);
            expect((result as any).data[0].teamName).toBe('Manchester United');
            expect((result as any).meta.totalItems).toBe(1);
      });

      it('should handle API fetch when DB is empty', async () => {
            jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);

            // Mock empty DB response
            const qb = {
                  andWhere: jest.fn().mockReturnThis(),
                  orderBy: jest.fn().mockReturnThis(),
                  skip: jest.fn().mockReturnThis(),
                  take: jest.fn().mockReturnThis(),
                  getManyAndCount: jest.fn().mockResolvedValueOnce([[], 0]),
            };
            jest.spyOn(standingRepository, 'createQueryBuilder').mockReturnValueOnce(qb as any);

            // Mock image service
            jest.spyOn(imageService, 'downloadImage').mockResolvedValue('/images/teams/33.png');

            const result = await service.getStandings({ league: 39, season: 2024, format: 'internal' });

            expect(standingRepository.createQueryBuilder).toHaveBeenCalled();
            expect((result as any).data).toEqual([]);
            expect((result as any).meta.totalItems).toBe(0);
      });
});
