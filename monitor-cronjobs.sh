#!/bin/bash

echo "🔍 MONITORING CRONJOBS IN REAL-TIME"
echo "==================================="
echo "Worker started at: $(date)"
echo "Cronjob schedule: Every 30 seconds"
echo ""

# Monitor for 2 minutes to catch cronjob activity
echo "Monitoring for 2 minutes..."
echo "Press Ctrl+C to stop monitoring"
echo ""

# Function to check for new logs
check_logs() {
    echo "--- $(date) ---"
    
    # Get recent logs (last 5 lines)
    RECENT_LOGS=$(pm2 logs auto-update-sports-game --lines 5 --nostream 2>/dev/null | grep -E "(sync|Cron|Starting|debug|SyncService)" | tail -3)
    
    if [ -n "$RECENT_LOGS" ]; then
        echo "🔍 Recent sync activity:"
        echo "$RECENT_LOGS"
    else
        echo "⏳ No sync activity detected yet..."
    fi
    
    # Check if there are fixtures that should trigger sync
    FIXTURES_COUNT=$(psql -h localhost -U postgresuser -d testlivesport -t -c "
    SELECT COUNT(*) FROM fixtures 
    WHERE date BETWEEN NOW() - INTERVAL '2 hours' AND NOW() + INTERVAL '30 minutes'
    AND data->>'status' NOT IN ('FT', 'CANC', 'AWD', 'PST');" 2>/dev/null | tr -d ' ')
    
    echo "📊 Fixtures in sync window: $FIXTURES_COUNT"
    echo ""
}

# Monitor for 2 minutes
for i in {1..8}; do
    check_logs
    
    if [ $i -lt 8 ]; then
        echo "Waiting 15 seconds for next check..."
        sleep 15
    fi
done

echo ""
echo "🔍 FINAL CHECK - All Worker Logs:"
echo "================================="
pm2 logs auto-update-sports-game --lines 20 --nostream | grep -E "(sync|Cron|Starting|debug|SyncService|LOG|ERROR)"

echo ""
echo "📊 SUMMARY:"
echo "==========="
echo "1. Worker is running: ✅"
echo "2. Startup logs visible: ✅"
echo "3. Enhanced logging enabled: ✅"

# Check if we saw any sync activity
SYNC_ACTIVITY=$(pm2 logs auto-update-sports-game --lines 50 --nostream 2>/dev/null | grep -c -E "(sync|SyncService)")

if [ "$SYNC_ACTIVITY" -gt 0 ]; then
    echo "4. Cronjob activity detected: ✅"
else
    echo "4. Cronjob activity detected: ❌"
    echo ""
    echo "🔍 POSSIBLE REASONS:"
    echo "- No fixtures in sync window (normal if no upcoming matches)"
    echo "- SyncService not properly initialized"
    echo "- Cronjob registration failed"
fi
