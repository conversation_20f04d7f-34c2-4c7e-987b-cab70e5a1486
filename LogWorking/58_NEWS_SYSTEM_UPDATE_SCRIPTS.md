# 🔧 News System Update Scripts & API Examples

## 🔑 **AUTHENTICATION**

T<PERSON><PERSON> cả update endpoints yêu cầu System Authentication:

```bash
# 1. Login để lấy JWT token
curl -X POST http://localhost:3000/system-auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123456"
  }'

# Response sẽ trả về:
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin"
  }
}
```

**Lưu access_token để sử dụng trong các request tiếp theo!**

---

## 📰 **UPDATE NEWS ARTICLE**

### **Endpoint:** `PATCH /admin/news/articles/{id}`
### **Authentication:** <PERSON><PERSON> Required

### **1. Update Article - Full Example:**

```bash
curl -X PATCH http://localhost:3000/admin/news/articles/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "Messi Signs New Contract with Barcelona - Updated",
    "excerpt": "Lionel Messi has officially signed a new 3-year contract with FC Barcelona, marking his return to the club where he became a legend.",
    "content": "<h1>Messi Returns to Barcelona</h1><p>In a surprising turn of events, Lionel Messi has decided to return to Barcelona after his stint at PSG. The new contract includes...</p><ul><li>3-year deal worth €50M per year</li><li>Performance bonuses</li><li>Ambassador role after retirement</li></ul>",
    "tags": ["messi", "barcelona", "contract", "transfer", "2025"],
    "status": "published",
    "publishedAt": "2025-06-02T10:00:00.000Z",
    "isFeatured": true,
    "priority": 9,
    "categoryId": 1,
    "relatedLeagueId": 140,
    "relatedTeamId": 529,
    "relatedPlayerId": 154,
    "featuredImage": "https://example.com/images/messi-barcelona-2025.jpg",
    "metaTitle": "Messi Returns to Barcelona - Official Contract Signing 2025",
    "metaDescription": "Breaking news: Lionel Messi signs new 3-year contract with Barcelona. Get all details about the historic return and contract terms."
  }'
```

### **2. Update Article - Partial Update:**

```bash
# Chỉ update title và status
curl -X PATCH http://localhost:3000/admin/news/articles/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "Updated Title Only",
    "status": "published"
  }'
```

### **3. Update Article - Change Category:**

```bash
# Chuyển article sang category khác
curl -X PATCH http://localhost:3000/admin/news/articles/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "categoryId": 2,
    "tags": ["match-report", "barcelona", "real-madrid"]
  }'
```

### **4. Update Article - Set Featured:**

```bash
# Đặt article làm featured với priority cao
curl -X PATCH http://localhost:3000/admin/news/articles/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "isFeatured": true,
    "priority": 10
  }'
```

---

## 🗂️ **UPDATE NEWS CATEGORY**

### **Endpoint:** `PATCH /admin/news/categories/{id}`
### **Authentication:** Bearer Token Required

### **1. Update Category - Full Example:**

```bash
curl -X PATCH http://localhost:3000/admin/news/categories/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Transfer News & Rumors",
    "description": "Latest transfer news, rumors, confirmations and market updates from top European leagues",
    "icon": "transfer-icon-v2",
    "color": "#FF6B35",
    "sortOrder": 1,
    "isActive": true,
    "isPublic": true,
    "metaTitle": "Transfer News - Latest Football Transfers & Rumors",
    "metaDescription": "Stay updated with the latest football transfer news, rumors, and confirmed deals from Premier League, La Liga, Serie A, and more."
  }'
```

### **2. Update Category - Change Display Order:**

```bash
# Thay đổi thứ tự hiển thị
curl -X PATCH http://localhost:3000/admin/news/categories/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "sortOrder": 5
  }'
```

### **3. Update Category - Toggle Status:**

```bash
# Tắt category (ẩn khỏi public)
curl -X PATCH http://localhost:3000/admin/news/categories/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "isActive": false,
    "isPublic": false
  }'
```

---

## 🎯 **SPECIAL UPDATE OPERATIONS**

### **1. Publish Article:**

```bash
curl -X POST http://localhost:3000/admin/news/articles/1/publish \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **2. Unpublish Article:**

```bash
curl -X POST http://localhost:3000/admin/news/articles/1/unpublish \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **3. Archive Article:**

```bash
curl -X POST http://localhost:3000/admin/news/articles/1/archive \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **4. Reorder Categories:**

```bash
curl -X POST http://localhost:3000/admin/news/categories/reorder \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '[
    { "id": 1, "sortOrder": 3 },
    { "id": 2, "sortOrder": 1 },
    { "id": 3, "sortOrder": 2 },
    { "id": 4, "sortOrder": 4 }
  ]'
```

### **5. Toggle Category Status:**

```bash
curl -X POST http://localhost:3000/admin/news/categories/1/toggle-status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "isActive": true
  }'
```

---

## 📊 **RESPONSE EXAMPLES**

### **Successful Article Update Response:**

```json
{
  "id": 1,
  "title": "Messi Signs New Contract with Barcelona - Updated",
  "slug": "messi-signs-new-contract-barcelona",
  "excerpt": "Lionel Messi has officially signed...",
  "content": "<h1>Messi Returns to Barcelona</h1>...",
  "featuredImage": "https://example.com/images/messi-barcelona-2025.jpg",
  "tags": ["messi", "barcelona", "contract", "transfer", "2025"],
  "status": "published",
  "publishedAt": "2025-06-02T10:00:00.000Z",
  "isFeatured": true,
  "priority": 9,
  "viewCount": 1250,
  "shareCount": 45,
  "likeCount": 89,
  "category": {
    "id": 1,
    "slug": "transfer-news",
    "name": "Transfer News",
    "color": "#FF6B35"
  },
  "relatedLeagueId": 140,
  "relatedTeamId": 529,
  "relatedPlayerId": 154,
  "metaTitle": "Messi Returns to Barcelona - Official Contract Signing 2025",
  "metaDescription": "Breaking news: Lionel Messi signs new 3-year contract...",
  "createdAt": "2025-06-01T08:00:00.000Z",
  "updatedAt": "2025-06-02T10:30:00.000Z"
}
```

### **Error Response Example:**

```json
{
  "message": "Article with ID 999 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

---

## 🔍 **VALIDATION RULES**

### **Article Update Validation:**
- `title`: 5-500 characters
- `content`: 10-50,000 characters  
- `tags`: Array of strings
- `priority`: 0-100
- `categoryId`: Must exist in database
- `status`: 'draft' | 'published' | 'archived'

### **Category Update Validation:**
- `name`: 2-200 characters
- `color`: Valid hex color (#RRGGBB)
- `sortOrder`: 0-9999
- `icon`: Max 500 characters

---

## 🚨 **COMMON ERRORS & SOLUTIONS**

### **401 Unauthorized:**
```bash
# Solution: Login lại để lấy token mới
curl -X POST http://localhost:3000/system-auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123456"}'
```

### **404 Not Found:**
```bash
# Solution: Kiểm tra ID có tồn tại
curl -X GET http://localhost:3000/admin/news/articles/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **400 Bad Request:**
```bash
# Solution: Kiểm tra validation rules và format JSON
# Ví dụ: categoryId phải tồn tại
curl -X GET http://localhost:3000/admin/news/categories \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 🎯 **QUICK TEST SCRIPT**

```bash
#!/bin/bash

# 1. Login và lấy token
TOKEN=$(curl -s -X POST http://localhost:3000/system-auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123456"}' \
  | jq -r '.access_token')

echo "Token: $TOKEN"

# 2. Update article
curl -X PATCH http://localhost:3000/admin/news/articles/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "Test Update from Script",
    "priority": 5,
    "isFeatured": true
  }' | jq '.'

# 3. Update category
curl -X PATCH http://localhost:3000/admin/news/categories/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Updated Category Name",
    "sortOrder": 1
  }' | jq '.'
```

**Lưu script này thành file `test_update.sh` và chạy: `bash test_update.sh`**
