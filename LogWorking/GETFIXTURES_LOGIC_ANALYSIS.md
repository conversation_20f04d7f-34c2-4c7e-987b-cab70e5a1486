# 🔍 Chi tiết Logic xử lý getFixtures

## **📋 Tổng quan**
Method `getFixtures` trong `FixtureService` là core method xử lý việc lấy fixtures với logic phức tạp bao gồm caching, database fallback, API fetching, và active league filtering.

## **🔄 Flow Logic chính**

### **1. Input Processing & Cache Key Generation**
```typescript
async getFixtures(query: GetFixturesDto): Promise<PaginatedFixturesResponse> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const cacheKey = `fixtures_list_active_${query.league ?? ''}_${query.season ?? ''}_${query.date ?? ''}_${query.team ?? ''}_${query.status ?? ''}_${query.isHot ?? ''}_${query.search ?? ''}_${page}_${limit}`;
```

**Cache Key Pattern:**
- `fixtures_list_active_` prefix
- Tất cả query parameters được serialize
- Pagination parameters (page, limit)

### **2. Branch 1: Force API Fetch (newdb=true)**
```typescript
if (query.newdb === true) {
    // 🔥 FORCE API FETCH - Bypass cache và database
    const fixtures = await this.fetchFromApi(query);
    
    if (fixtures.length > 0) {
        // Upsert vào database
        await this.fixtureRepository.upsert(fixtures, ['externalId']);
        // Re-fetch từ DB để có pagination chính xác
        paginatedResult = await this.fetchFromDb(query);
    }
    
    // Clear cache pattern để force refresh
    await this.cacheService.deleteByPattern(`fixtures_list_active_${query.league ?? ''}_${query.season ?? ''}_${query.date ?? ''}_${query.team ?? ''}_*`);
    
    // Cache kết quả mới (TTL: 1 hour)
    await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
    return response;
}
```

**Đặc điểm Branch 1:**
- **Bypass hoàn toàn** cache và database
- **Force fetch** từ API
- **Upsert** data mới vào DB
- **Clear cache pattern** để refresh
- **Re-cache** với TTL 1 giờ

### **3. Branch 2: Normal Flow (Cache → DB → API)**
```typescript
// Step 1: Check cache first
const cached = await this.cacheService.getCache(cacheKey);
if (cached) {
    return JSON.parse(cached);
}

// Step 2: Try database
let { fixtures, totalItems } = await this.fetchFromDb(query);

// Step 3: Fallback to API if DB empty
if (fixtures.length === 0 && !query.id) {
    fixtures = await this.fetchFromApi(query);
    if (fixtures.length > 0) {
        await this.fixtureRepository.save(fixtures);
        // Re-fetch from DB for accurate pagination
        const paginatedResult = await this.fetchFromDb(query);
        fixtures = paginatedResult.fixtures;
        totalItems = paginatedResult.totalItems;
    }
}

// Step 4: Cache result
await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
```

**Flow Priority:**
1. **Cache** (TTL: 1 hour) → Return immediately
2. **Database** → Query với active league filtering
3. **API Fallback** → Chỉ khi DB empty và không phải query by ID
4. **Cache Result** → Store cho lần sau

## **🗄️ Database Query Logic (fetchFromDb)**

### **Active League Filtering**
```typescript
const qb = this.fixtureRepository.createQueryBuilder('fixture');

// ✅ CORE: Join với leagues table để filter active leagues
qb.innerJoin('leagues', 'league', 'league.externalId = fixture.leagueId AND league.season = fixture.season')
  .andWhere('league.active = :active', { active: true });
```

### **Dynamic Query Building**
```typescript
if (query.id) qb.andWhere('fixture.externalId = :id', { id: query.id });
if (query.league) qb.andWhere('fixture.leagueId = :league', { league: query.league });
if (query.season) qb.andWhere('fixture.season = :season', { season: query.season });
if (query.date) qb.andWhere('DATE(fixture.date) = :date', { date: query.date });
if (query.team) qb.andWhere('fixture.homeTeamId = :team OR fixture.awayTeamId = :team', { team: query.team });
if (query.status) qb.andWhere('fixture.data->>\'status\' = :status', { status: query.status });
if (query.isHot !== undefined) qb.andWhere('fixture.isHot = :isHot', { isHot: query.isHot });

// Search trong team names
if (query.search) {
    const searchTerm = `%${query.search.toLowerCase()}%`;
    qb.andWhere(
        '(LOWER(fixture.data->>\'homeTeamName\') LIKE :searchTerm OR LOWER(fixture.data->>\'awayTeamName\') LIKE :searchTerm)',
        { searchTerm }
    );
}
```

### **Pagination & Ordering**
```typescript
qb.orderBy('fixture.date', 'ASC')
  .skip((page - 1) * limit)
  .take(limit);

const [fixtures, totalItems] = await qb.getManyAndCount();
```

## **🌐 API Fetch Logic (fetchFromApi)**

### **Step 1: API Request Preparation**
```typescript
// Remove pagination params (API Football không support)
const { page, limit, newdb, isHot, status, ...apiQuery } = query;

const response = await axios.get(`${apiFootballUrl}/fixtures`, {
    params: { ...apiQuery, timezone: 'UTC' },
    headers: { 'x-apisports-key': apiKey },
});
```

### **Step 2: Parallel Data Collection**
```typescript
const leagueMap = new Map<string, Partial<League>>();
const teamMap = new Map<number, Partial<Team>>();

await Promise.all(
    response.data.response.map(async (apiData: any) => {
        // Download images và collect league data
        const leagueLogoPath = await this.imageService.downloadImage(apiData.league.logo, 'leagues', `${apiData.league.id}.png`);
        
        // Download team logos
        const homeTeamLogoPath = await this.imageService.downloadImage(apiData.teams.home.logo, 'teams', `${apiData.teams.home.id}.png`);
    })
);
```

### **Step 3: Active League Validation**
```typescript
// Kiểm tra leagues active trong database
const activeLeagues = await this.getActiveLeagues([...leagueMap.keys()]);

// Chỉ process leagues active
const activeLeagueData = [...leagueMap.entries()]
    .filter(([key]) => activeLeagues.has(key))
    .map(([, league]) => league);
```

### **Step 4: Database Transaction**
```typescript
await this.dataSource.transaction(async (manager) => {
    await Promise.all([
        this.processLeagues(activeLeagueData, manager.getRepository(League)),
        this.processTeams([...teamMap.values()], manager.getRepository(Team)),
    ]);
});
```

### **Step 5: Fixture Processing**
```typescript
const fixtures = await Promise.all(
    response.data.response.map(async (apiData: any) => {
        // Kiểm tra league active
        const leagueKey = `${apiData.league.id}_${apiData.league.season}`;
        if (!activeLeagues.has(leagueKey)) {
            return null; // Skip inactive league fixtures
        }
        
        // Map API data to Fixture entity
        const fixture = new Fixture();
        fixture.externalId = apiData.fixture.id;
        fixture.data = {
            homeTeamName: apiData.teams.home.name,
            status: apiData.fixture.status?.short || 'NS',
            // ... other fields
        };
        return fixture;
    })
);
```

## **🎯 Key Features**

### **1. Smart Caching Strategy**
- **TTL**: 1 hour cho normal queries
- **Cache Key**: Include tất cả query parameters
- **Cache Invalidation**: Pattern-based clearing
- **Cache Bypass**: `newdb=true` parameter

### **2. Active League Filtering**
- **Database Level**: INNER JOIN với leagues table
- **API Level**: Pre-validate active leagues trước khi process
- **Consistency**: Đảm bảo chỉ active leagues được return

### **3. Fallback Strategy**
- **Cache First**: Fastest response
- **Database Second**: Reliable local data
- **API Last**: Fresh data khi cần thiết
- **Error Handling**: Graceful degradation

### **4. Performance Optimizations**
- **Parallel Processing**: Images download, data collection
- **Transaction Safety**: Database operations trong transaction
- **Upsert Strategy**: Avoid duplicates
- **Pagination**: Accurate count với getManyAndCount()

### **5. Data Integrity**
- **Image Downloads**: Automatic image caching
- **Slug Generation**: SEO-friendly URLs
- **UTC Timezone**: Consistent time handling
- **Validation**: Comprehensive data validation

## **📊 Performance Characteristics**

| Scenario | Cache | DB Query | API Call | Total Time |
|----------|-------|----------|----------|------------|
| **Cache Hit** | ✅ | ❌ | ❌ | ~5ms |
| **DB Hit** | ❌ | ✅ | ❌ | ~50ms |
| **API Fallback** | ❌ | ✅ | ✅ | ~2-5s |
| **Force API** | ❌ | ✅ | ✅ | ~2-5s |

## **🔧 Usage Patterns**

### **Normal Query:**
```bash
GET /football/fixtures?league=39&season=2024&page=1&limit=10
```

### **Force Refresh:**
```bash
GET /football/fixtures?league=39&season=2024&newdb=true
```

### **Search Query:**
```bash
GET /football/fixtures?search=manchester&page=1&limit=5
```

### **Team Schedule:**
```bash
GET /football/fixtures?team=33&season=2024
```
