# 🔧 News System Test Cases - Fixes Needed

## 📊 **Current Test Status**

### ✅ **PASSING TESTS (51/55 test cases)**
- `test/news/categories/news-category.entity.spec.ts` ✅ **4 tests**
- `test/news/categories/news-category.service.spec.ts` ✅ **13 tests**  
- `test/news/categories/public-news-category.controller.spec.ts` ✅ **18 tests**
- `test/news/articles/news-article.service.spec.ts` ✅ **16 tests**

### ❌ **FAILING TESTS (4/55 test cases)**
- `test/news/articles/news-article.entity.spec.ts` ❌ **4 tests failing**
- `test/news/categories/news-category.controller.spec.ts` ❌ **TypeScript compilation error**
- `test/news/articles/news-article.controller.spec.ts` ❌ **TypeScript compilation error**
- `test/news/articles/public-news-article.controller.spec.ts` ❌ **TypeScript compilation error**

---

## 🚨 **Critical Issues to Fix**

### **1. Entity Test Issues**
**File**: `test/news/articles/news-article.entity.spec.ts`
**Problem**: `categoryId` is required but not provided in test data
**Error**: `null value in column "categoryId" of relation "news_articles" violates not-null constraint`

**Fix Required**:
```typescript
// Add categoryId to all article test data
const articleData: Partial<NewsArticle> = {
    title: 'Test Article',
    slug: 'test-article',
    content: 'Test content',
    categoryId: 1, // ← ADD THIS
    authorId: 1,
};
```

### **2. SystemUser Type Issues**
**Files**: 
- `test/news/categories/news-category.controller.spec.ts`
- `test/news/articles/news-article.controller.spec.ts`

**Problem**: Missing `createdBy` field in SystemUser mock
**Error**: `Property 'createdBy' is missing in type 'SystemUser'`

**Fix Required**:
```typescript
const mockUser: SystemUser = {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    // ... other fields
    createdBy: 1, // ← ADD THIS
    isAdmin: jest.fn(),
    canEditContent: jest.fn(),
    canModerate: jest.fn(),
};
```

### **3. Response DTO Type Mismatches**
**Files**: 
- `test/news/articles/news-article.controller.spec.ts`
- `test/news/articles/public-news-article.controller.spec.ts`

**Problems**:
- Pagination structure: `total` vs `meta.totalItems`
- Missing `category` field in ArticleResponseDto
- Tags field type: `string[]` vs `string`

**Fix Required**:
```typescript
// Fix pagination structure
const mockPaginatedResponse = {
    data: [mockArticleResponse],
    meta: {
        totalItems: 1,
        totalPages: 1,
        currentPage: 1,
        limit: 10,
    },
    status: 200,
};

// Add category to article response
const mockArticleResponse = {
    // ... other fields
    category: {
        id: 1,
        slug: 'test-category',
        name: 'Test Category',
        // ... complete category fields
    },
};
```

---

## 🛠️ **Detailed Fix Plan**

### **Phase 1: Fix Entity Tests**
1. **Add categoryId to article entity tests**
   - Create test category first
   - Use category.id in article data
   - Fix all 4 failing entity tests

### **Phase 2: Fix SystemUser Mocks**
1. **Update SystemUser mock objects**
   - Add missing `createdBy` field
   - Ensure all required fields are present
   - Fix TypeScript compilation errors

### **Phase 3: Fix Response DTO Types**
1. **Update pagination responses**
   - Change from `total` to `meta.totalItems`
   - Add `status` field to responses
   - Fix all pagination expectations

2. **Fix ArticleResponseDto**
   - Add complete `category` field
   - Fix `tags` field type (string[] vs string)
   - Ensure all required fields are present

### **Phase 4: Fix Public Controller Tests**
1. **Update PublicArticlesDto**
   - Fix tags field type
   - Update getFeaturedArticles parameter type
   - Fix all service mock calls

---

## 📝 **Step-by-Step Implementation**

### **Step 1: Fix Article Entity Tests**
```bash
# Edit: test/news/articles/news-article.entity.spec.ts
# Add categoryId to all test data
```

### **Step 2: Fix SystemUser Mocks**
```bash
# Edit: test/news/categories/news-category.controller.spec.ts
# Edit: test/news/articles/news-article.controller.spec.ts
# Add createdBy field to mockUser
```

### **Step 3: Fix Response Types**
```bash
# Edit: test/news/articles/news-article.controller.spec.ts
# Edit: test/news/articles/public-news-article.controller.spec.ts
# Update pagination and response structures
```

### **Step 4: Run Tests**
```bash
npm test -- test/news/
```

---

## 🎯 **Expected Outcome**

After fixes:
- ✅ **8/8 Test Files PASSING**
- ✅ **55+ Test Cases PASSING**
- ✅ **Complete TypeScript Compilation**
- ✅ **All Entity, Service, Controller Tests Working**

---

## 🚀 **Next Steps After Fixes**

1. **Integration Tests**: Complete e2e testing
2. **Performance Tests**: Load testing for endpoints
3. **Security Tests**: Authentication/authorization
4. **CI/CD Integration**: Add to automated pipeline

---

## 📊 **Progress Tracking**

- [x] Entity Tests (Categories) ✅
- [x] Service Tests (Categories) ✅  
- [x] Service Tests (Articles) ✅
- [x] Public Controller Tests (Categories) ✅
- [ ] Entity Tests (Articles) ❌ **← NEXT**
- [ ] Controller Tests (Categories) ❌
- [ ] Controller Tests (Articles) ❌
- [ ] Public Controller Tests (Articles) ❌
- [ ] Integration Tests ⏳
- [ ] Performance Tests ⏳

**Current Progress**: 51/55 tests passing (93% complete)
**Estimated Time to Complete**: 2-3 hours for remaining fixes
